# 額外班成功解決方案詳細記錄

## 🎯 目的
記錄額外班餘額問題的成功解決過程，作為修正事病假問題的精確範本。

## 📋 額外班問題與解決

### **原始問題**
額外班餘額曾經有與事病假完全相同的問題：
- 各月份顯示的餘額不一致
- 重新編輯時餘額計算錯誤
- 審核後餘額同步問題

### **成功解決方案**

#### **1. 正確的餘額顯示邏輯**
```javascript
// ✅ 額外班的正確做法
function updateOvertimeBalanceDisplay() {
    const balanceDisplay = document.getElementById('overtime-balance-display');
    const compensatoryOption = document.getElementById('compensatory-option');

    if (balanceDisplay && currentUser) {
        const currentCompensatoryDays = Object.values(currentScheduleData).filter(shift => shift.type === 'compensatory').length;
        const totalBalance = currentUser.overtimeBalance || 0; // 關鍵：直接使用用戶餘額

        // 計算將要扣除或恢復的補休天數
        let willDeduct = 0;
        let willRestore = 0;
        let finalBalance = totalBalance;

        if (window.originalCompensatoryDays !== undefined) {
            // 如果是重新編輯，計算差額
            const originalDays = window.originalCompensatoryDays;
            const difference = currentCompensatoryDays - originalDays;

            if (difference > 0) {
                // 新增補休，需要扣除
                willDeduct = difference;
                finalBalance = totalBalance - willDeduct;
            } else if (difference < 0) {
                // 減少補休，需要恢復
                willRestore = Math.abs(difference);
                finalBalance = totalBalance + willRestore;
            }
        } else {
            // 新申請，直接扣除
            willDeduct = currentCompensatoryDays;
            finalBalance = totalBalance - willDeduct;
        }

        // 顯示詳細的餘額資訊
        if (willDeduct > 0) {
            balanceDisplay.innerHTML = `
                <span class="${finalBalance < 0 ? 'text-red-600' : 'text-blue-600'}">
                    將扣除 ${willDeduct} 天，剩餘 ${Math.max(0, finalBalance)} 天
                </span>
            `;
        } else if (willRestore > 0) {
            balanceDisplay.innerHTML = `
                <span class="text-green-600">
                    將恢復 ${willRestore} 天，剩餘 ${finalBalance} 天
                </span>
            `;
        } else {
            balanceDisplay.textContent = `${totalBalance} 天`;
        }
    }
}
```

#### **2. 重新編輯時的正確處理**
```javascript
// ✅ 額外班重新編輯的正確邏輯
if (existingRequest && existingRequest.status === 'approved') {
    // 只記錄原始天數，不恢復餘額
    window.originalCompensatoryDays = existingRequest.compensatoryDaysCount || 0;
    console.log(`記錄原始補休天數: ${window.originalCompensatoryDays}`);
    
    // 關鍵：不在這裡恢復餘額，保持實際餘額不變
    // 餘額顯示會基於原始天數計算差額預覽
}
```

#### **3. 審核批准時的正確處理**
```javascript
// ✅ 額外班審核批准的正確邏輯
if (request.hasCompensatoryLeave && request.compensatoryDaysCount > 0) {
    const employeeIndex = systemData.users.findIndex(u => u.id === request.employeeId);
    if (employeeIndex !== -1) {
        // 關鍵：只有在審核批准時才真正調整餘額
        const oldBalance = systemData.users[employeeIndex].overtimeBalance || 0;
        systemData.users[employeeIndex].overtimeBalance = oldBalance - request.compensatoryDaysCount;
        
        console.log(`批准申請 - 扣減員工 ${request.employeeName} 額外班餘額: ${oldBalance} → ${systemData.users[employeeIndex].overtimeBalance}`);
        
        // 同步當前用戶的餘額
        if (currentUser && currentUser.id === request.employeeId) {
            currentUser.overtimeBalance = systemData.users[employeeIndex].overtimeBalance;
        }
    }
}
```

## 🔧 關鍵成功要素

### **1. 餘額計算原則**
- **直接使用實際餘額**：`const totalBalance = currentUser.overtimeBalance || 0;`
- **不扣除當前選擇**：不在顯示時扣除當前編輯的天數
- **差額計算預覽**：基於原始天數計算變化預覽

### **2. 重新編輯原則**
- **只記錄原始天數**：`window.originalCompensatoryDays = existingRequest.compensatoryDaysCount || 0;`
- **不恢復餘額**：保持實際餘額不變
- **預覽機制**：顯示將要發生的變化

### **3. 審核生效原則**
- **延遲調整**：只有審核批准後才調整實際餘額
- **同步更新**：調整後同步所有相關變數
- **詳細記錄**：Console 輸出追蹤變化過程

## ❌ 事病假的錯誤對比

### **錯誤的餘額計算**
```javascript
// ❌ 事病假的錯誤做法
function updateSickLeaveBalanceDisplay() {
    const currentSickLeaveDays = Object.values(currentScheduleData).filter(shift => shift.type === 'sick').length;
    const totalQuota = currentUser.annualSickLeaveQuota || 0;
    const usedDays = currentUser.usedSickLeaveDays || 0;
    
    // 錯誤：將當前選擇的天數也扣除了
    const availableBalance = totalQuota - usedDays - currentSickLeaveDays;
    
    balanceDisplay.textContent = availableBalance;
}
```

### **問題分析**
1. **錯誤扣除**：`- currentSickLeaveDays` 導致各月份顯示不同
2. **缺少預覽**：沒有基於原始天數的差額計算
3. **邏輯混亂**：沒有區分實際餘額和預覽變化

## 🎯 事病假修正方案

### **完全仿照額外班的正確邏輯**
```javascript
// ✅ 事病假應該使用的正確邏輯
function updateSickLeaveBalanceDisplay() {
    const balanceDisplay = document.getElementById('sick-leave-balance-display');
    const sickLeaveOption = document.getElementById('sick-leave-option');

    if (balanceDisplay && currentUser) {
        const currentSickLeaveDays = Object.values(currentScheduleData).filter(shift => shift.type === 'sick').length;
        const totalQuota = currentUser.annualSickLeaveQuota || 0;
        const usedDays = currentUser.usedSickLeaveDays || 0;
        const totalBalance = totalQuota - usedDays; // 關鍵：實際餘額，不扣除當前選擇

        // 計算將要扣除或恢復的事病假天數（完全仿照額外班）
        let willDeduct = 0;
        let willRestore = 0;
        let finalBalance = totalBalance;

        if (window.originalSickLeaveDays !== undefined) {
            // 如果是重新編輯，計算差額
            const originalDays = window.originalSickLeaveDays;
            const difference = currentSickLeaveDays - originalDays;

            if (difference > 0) {
                // 新增事病假，需要扣除
                willDeduct = difference;
                finalBalance = totalBalance - willDeduct;
            } else if (difference < 0) {
                // 減少事病假，需要恢復
                willRestore = Math.abs(difference);
                finalBalance = totalBalance + willRestore;
            }
        } else {
            // 新申請，直接扣除
            willDeduct = currentSickLeaveDays;
            finalBalance = totalBalance - willDeduct;
        }

        // 顯示詳細的餘額資訊（完全仿照額外班）
        if (willDeduct > 0) {
            balanceDisplay.innerHTML = `
                <span class="${finalBalance < 0 ? 'text-red-600' : 'text-yellow-600'}">
                    將扣除 ${willDeduct} 天，剩餘 ${Math.max(0, finalBalance)} 天
                </span>
            `;
        } else if (willRestore > 0) {
            balanceDisplay.innerHTML = `
                <span class="text-green-600">
                    將恢復 ${willRestore} 天，剩餘 ${finalBalance} 天
                </span>
            `;
        } else {
            balanceDisplay.textContent = `${totalBalance} 天`;
        }
    }
}
```

## 📊 測試驗證

### **測試場景**
員工A：年度事病假額度30天，已用12天，實際餘額18天
- 7月已批准申請：包含1天事病假
- 8月已批准申請：包含2天事病假

### **修正前的錯誤行為**
- 進入7月編輯：顯示餘額 = 18 - 1 = 17天 ❌
- 進入8月編輯：顯示餘額 = 18 - 2 = 16天 ❌

### **修正後的正確行為**
- 進入7月編輯：顯示餘額 = 18天 ✅
- 進入8月編輯：顯示餘額 = 18天 ✅
- 新增事病假時：顯示 `將扣除 1 天，剩餘 17 天` ✅
- 減少事病假時：顯示 `將恢復 1 天，剩餘 19 天` ✅

---

**文檔版本**：v1.0
**建立日期**：2024年12月
**用途**：事病假問題修正的精確範本
