<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理系統 - 登入測試</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="flex justify-center">
                    <i data-lucide="calendar" class="h-12 w-12 text-indigo-600"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    登入您的帳戶
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    或 <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">申請新帳戶</a>
                </p>
            </div>

            <!-- 測試帳號提示 -->
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 class="text-sm font-medium text-blue-800 mb-2">測試帳號：</h3>
                <div class="text-xs text-blue-700 space-y-1">
                    <div>管理員: <EMAIL> / admin123</div>
                    <div>主管: <EMAIL> / super123</div>
                    <div>員工: <EMAIL> / emp123</div>
                </div>
            </div>

            <form class="mt-8 space-y-6" onsubmit="handleLogin(event)">
                <!-- 錯誤訊息 -->
                <div id="error-message" class="hidden bg-red-50 border border-red-200 rounded-md p-4 flex items-center">
                    <i data-lucide="alert-circle" class="h-5 w-5 text-red-400 mr-2"></i>
                    <span class="text-sm text-red-700" id="error-text"></span>
                </div>

                <!-- 成功訊息 -->
                <div id="success-message" class="hidden bg-green-50 border border-green-200 rounded-md p-4">
                    <span class="text-sm text-green-700" id="success-text"></span>
                </div>

                <div class="rounded-md shadow-sm -space-y-px">
                    <div class="relative">
                        <label for="email" class="sr-only">電子郵件</label>
                        <i data-lucide="mail" class="absolute left-3 top-3 h-5 w-5 text-gray-400"></i>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            autocomplete="email"
                            required
                            class="appearance-none rounded-none relative block w-full px-10 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                            placeholder="電子郵件地址"
                        />
                    </div>
                    <div class="relative">
                        <label for="password" class="sr-only">密碼</label>
                        <i data-lucide="lock" class="absolute left-3 top-3 h-5 w-5 text-gray-400"></i>
                        <input
                            id="password"
                            name="password"
                            type="password"
                            autocomplete="current-password"
                            required
                            class="appearance-none rounded-none relative block w-full px-10 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                            placeholder="密碼"
                        />
                        <button
                            type="button"
                            class="absolute right-3 top-3 h-5 w-5 text-gray-400 hover:text-gray-600"
                            onclick="togglePassword()"
                        >
                            <i data-lucide="eye" id="eye-icon"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember-me"
                            name="remember-me"
                            type="checkbox"
                            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                            記住我
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                            忘記密碼？
                        </a>
                    </div>
                </div>

                <div>
                    <button
                        type="submit"
                        id="login-button"
                        class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        登入
                    </button>
                </div>
            </form>

            <!-- 測試結果顯示區域 -->
            <div id="test-results" class="hidden bg-gray-50 border border-gray-200 rounded-md p-4">
                <h3 class="text-sm font-medium text-gray-900 mb-2">登入測試結果：</h3>
                <div id="user-info" class="text-sm text-gray-700"></div>
                <div class="mt-4 space-y-2">
                    <button onclick="showDashboard('employee')" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                        查看員工儀表板
                    </button>
                    <button onclick="showDashboard('supervisor')" class="w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700">
                        查看主管儀表板
                    </button>
                    <button onclick="showDashboard('admin')" class="w-full bg-purple-600 text-white px-4 py-2 rounded-md text-sm hover:bg-purple-700">
                        查看管理員儀表板
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 儀表板模擬區域 -->
    <div id="dashboard-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="dashboard-title">儀表板</h3>
                    <button onclick="closeDashboard()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>
                <div id="dashboard-content" class="text-sm text-gray-700">
                    <!-- 儀表板內容將在這裡顯示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模擬用戶資料
        const mockUsers = [
            {
                id: '1',
                email: '<EMAIL>',
                password: 'admin123',
                name: '系統管理員',
                role: 'admin',
                location: 'ciguang',
                approved: true
            },
            {
                id: '2',
                email: '<EMAIL>',
                password: 'super123',
                name: '主管',
                role: 'supervisor',
                location: 'ciguang',
                approved: true
            },
            {
                id: '3',
                email: '<EMAIL>',
                password: 'emp123',
                name: '員工',
                role: 'employee',
                location: 'ruiguang',
                approved: true
            }
        ];

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.setAttribute('data-lucide', 'eye-off');
            } else {
                passwordInput.type = 'password';
                eyeIcon.setAttribute('data-lucide', 'eye');
            }
            lucide.createIcons();
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            const successDiv = document.getElementById('success-message');
            
            successDiv.classList.add('hidden');
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            const successText = document.getElementById('success-text');
            const errorDiv = document.getElementById('error-message');
            
            errorDiv.classList.add('hidden');
            successText.textContent = message;
            successDiv.classList.remove('hidden');
        }

        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const button = document.getElementById('login-button');
            
            // 顯示載入狀態
            button.textContent = '登入中...';
            button.disabled = true;
            
            // 模擬網路延遲
            setTimeout(() => {
                const user = mockUsers.find(u => u.email === email && u.password === password);
                
                if (user) {
                    if (!user.approved) {
                        showError('您的帳戶尚未通過審核，請聯繫管理員。');
                    } else {
                        showSuccess(`登入成功！歡迎回來，${user.name}`);
                        
                        // 顯示用戶資訊
                        const userInfo = document.getElementById('user-info');
                        userInfo.innerHTML = `
                            <div><strong>姓名：</strong>${user.name}</div>
                            <div><strong>角色：</strong>${getRoleText(user.role)}</div>
                            <div><strong>地點：</strong>${user.location === 'ciguang' ? '慈光' : '瑞光'}</div>
                            <div><strong>狀態：</strong>已核准</div>
                        `;
                        
                        document.getElementById('test-results').classList.remove('hidden');
                    }
                } else {
                    showError('電子郵件或密碼錯誤');
                }
                
                // 恢復按鈕狀態
                button.textContent = '登入';
                button.disabled = false;
            }, 1000);
        }

        function getRoleText(role) {
            switch (role) {
                case 'admin': return '系統管理員';
                case 'supervisor': return '主管';
                case 'employee': return '員工';
                default: return '未知';
            }
        }

        function showDashboard(role) {
            const modal = document.getElementById('dashboard-modal');
            const title = document.getElementById('dashboard-title');
            const content = document.getElementById('dashboard-content');
            
            title.textContent = `${getRoleText(role)}儀表板`;
            
            let dashboardContent = '';
            
            switch (role) {
                case 'employee':
                    dashboardContent = `
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">員工功能</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-blue-900">新增排班申請</h5>
                                    <p class="text-sm text-blue-700 mt-1">提交下月排班需求</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-green-900">調班申請</h5>
                                    <p class="text-sm text-green-700 mt-1">申請臨時調班</p>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-purple-900">工時報表</h5>
                                    <p class="text-sm text-purple-700 mt-1">查看工時統計</p>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h5 class="font-medium text-gray-900">我的排班申請</h5>
                                <div class="mt-2 space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-white rounded border">
                                        <span class="text-sm">2025年1月排班</span>
                                        <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">待審核</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-white rounded border">
                                        <span class="text-sm">2024年12月排班</span>
                                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已核准</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'supervisor':
                    dashboardContent = `
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">主管功能</h4>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-blue-600">8</div>
                                    <div class="text-sm text-blue-700">總員工數</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-yellow-600">3</div>
                                    <div class="text-sm text-yellow-700">待審核申請</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-green-600">12</div>
                                    <div class="text-sm text-green-700">本月已核准</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-purple-600">慈光2/瑞光1</div>
                                    <div class="text-sm text-purple-700">今日人力</div>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h5 class="font-medium text-gray-900">待處理申請</h5>
                                <div class="mt-2 space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-white rounded border">
                                        <div>
                                            <span class="text-sm font-medium">張小明 - 排班申請</span>
                                            <div class="text-xs text-gray-500">慈光 • 2025年1月 • 15個班次</div>
                                        </div>
                                        <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">高優先度</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-white rounded border">
                                        <div>
                                            <span class="text-sm font-medium">李小華 - 調班申請</span>
                                            <div class="text-xs text-gray-500">瑞光 • 申請日期: 2024-12-15</div>
                                        </div>
                                        <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">中優先度</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'admin':
                    dashboardContent = `
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900">系統管理功能</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-blue-600">15</div>
                                    <div class="text-sm text-blue-700">總用戶數</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-yellow-600">3</div>
                                    <div class="text-sm text-yellow-700">待審核註冊</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-green-600">正常</div>
                                    <div class="text-sm text-green-700">系統狀態</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-gray-900">管理功能</h5>
                                    <div class="mt-2 space-y-2">
                                        <div class="p-2 bg-white rounded border text-sm">用戶管理</div>
                                        <div class="p-2 bg-white rounded border text-sm">系統設定</div>
                                        <div class="p-2 bg-white rounded border text-sm">資料備份</div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-gray-900">最近活動</h5>
                                    <div class="mt-2 space-y-2">
                                        <div class="text-xs text-gray-600">新用戶註冊：李小華 (瑞光) - 2小時前</div>
                                        <div class="text-xs text-gray-600">排班申請已核准：張小明 (慈光) - 4小時前</div>
                                        <div class="text-xs text-gray-600">系統自動備份完成 - 6小時前</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            content.innerHTML = dashboardContent;
            modal.classList.remove('hidden');
        }

        function closeDashboard() {
            document.getElementById('dashboard-modal').classList.add('hidden');
        }

        // 初始化圖示
        lucide.createIcons();
    </script>
</body>
</html>
