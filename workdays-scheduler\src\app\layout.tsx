import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "排班管理系統",
  description: "企業排班管理系統 - 支援員工排班申請、主管審批、調班管理等功能",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-TW">
      <body className="antialiased bg-gray-50 min-h-screen">
        {children}
      </body>
    </html>
  );
}
