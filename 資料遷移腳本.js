/**
 * 工作排班管理系統 - 資料遷移腳本
 * 從 localStorage 遷移到 Google Sheets
 * 版本：v1.0.0
 * 作者：Augment Agent
 * 日期：2024-12
 */

// ==================== 資料遷移主類別 ====================

class DataMigration {
    constructor() {
        this.migrationStatus = {
            total: 0,
            completed: 0,
            errors: [],
            startTime: null,
            endTime: null
        };
    }

    /**
     * 開始完整資料遷移
     */
    async startMigration() {
        try {
            this.migrationStatus.startTime = new Date();
            this.migrationStatus.errors = [];
            
            console.log('🚀 開始資料遷移...');
            
            // 1. 檢查 localStorage 資料
            const localData = this.loadLocalStorageData();
            if (!localData) {
                throw new Error('未找到 localStorage 資料');
            }
            
            console.log('📊 找到本地資料:', {
                users: localData.users?.length || 0,
                scheduleRequests: localData.scheduleRequests?.length || 0,
                overtimeRecords: localData.overtimeRecords?.length || 0,
                notifications: localData.notifications?.length || 0
            });
            
            // 2. 計算總遷移項目數
            this.calculateTotalItems(localData);
            
            // 3. 備份本地資料
            this.backupLocalData(localData);
            
            // 4. 執行分步遷移
            await this.migrateUsers(localData.users || []);
            await this.migrateScheduleRequests(localData.scheduleRequests || []);
            await this.migrateOvertimeRecords(localData.overtimeRecords || []);
            await this.migrateNotifications(localData.notifications || []);
            await this.migrateSystemSettings(localData.settings || {});
            
            // 5. 驗證遷移結果
            await this.validateMigration();
            
            this.migrationStatus.endTime = new Date();
            const duration = this.migrationStatus.endTime - this.migrationStatus.startTime;
            
            console.log('✅ 資料遷移完成!', {
                總項目: this.migrationStatus.total,
                已完成: this.migrationStatus.completed,
                錯誤數: this.migrationStatus.errors.length,
                耗時: `${Math.round(duration / 1000)}秒`
            });
            
            return {
                success: true,
                status: this.migrationStatus,
                message: '資料遷移成功完成'
            };
            
        } catch (error) {
            console.error('❌ 資料遷移失敗:', error);
            this.migrationStatus.endTime = new Date();
            this.migrationStatus.errors.push({
                type: 'MIGRATION_FAILED',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            
            return {
                success: false,
                status: this.migrationStatus,
                message: `資料遷移失敗: ${error.message}`
            };
        }
    }

    /**
     * 載入 localStorage 資料
     */
    loadLocalStorageData() {
        try {
            const savedData = localStorage.getItem('workdaysSystemData');
            if (!savedData) {
                return null;
            }
            
            return JSON.parse(savedData);
        } catch (error) {
            console.error('載入本地資料失敗:', error);
            return null;
        }
    }

    /**
     * 計算總遷移項目數
     */
    calculateTotalItems(localData) {
        this.migrationStatus.total = 
            (localData.users?.length || 0) +
            (localData.scheduleRequests?.length || 0) +
            (localData.overtimeRecords?.length || 0) +
            (localData.notifications?.length || 0) +
            1; // 系統設定
    }

    /**
     * 備份本地資料
     */
    backupLocalData(localData) {
        try {
            const backupData = {
                ...localData,
                backupDate: new Date().toISOString(),
                version: 'v0.9.1'
            };
            
            localStorage.setItem('workdaysSystemData_backup', JSON.stringify(backupData));
            console.log('💾 本地資料已備份');
        } catch (error) {
            console.warn('備份本地資料失敗:', error);
        }
    }

    /**
     * 遷移用戶資料
     */
    async migrateUsers(users) {
        console.log('👥 開始遷移用戶資料...');
        
        for (const user of users) {
            try {
                // 轉換用戶資料格式
                const migratedUser = this.transformUserData(user);
                
                // 發送到 Google Sheets
                await window.workScheduleAPI.createUser(migratedUser);
                
                this.migrationStatus.completed++;
                console.log(`✓ 用戶遷移完成: ${user.name}`);
                
            } catch (error) {
                console.error(`✗ 用戶遷移失敗: ${user.name}`, error);
                this.migrationStatus.errors.push({
                    type: 'USER_MIGRATION_FAILED',
                    userId: user.id,
                    userName: user.name,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    }

    /**
     * 遷移排班申請資料
     */
    async migrateScheduleRequests(requests) {
        console.log('📅 開始遷移排班申請資料...');
        
        for (const request of requests) {
            try {
                // 轉換排班申請資料格式
                const migratedRequest = this.transformScheduleRequestData(request);
                
                // 發送到 Google Sheets
                await window.workScheduleAPI.createScheduleRequest(migratedRequest);
                
                this.migrationStatus.completed++;
                console.log(`✓ 排班申請遷移完成: ${request.id}`);
                
            } catch (error) {
                console.error(`✗ 排班申請遷移失敗: ${request.id}`, error);
                this.migrationStatus.errors.push({
                    type: 'SCHEDULE_REQUEST_MIGRATION_FAILED',
                    requestId: request.id,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    }

    /**
     * 遷移額外班記錄資料
     */
    async migrateOvertimeRecords(records) {
        console.log('⏰ 開始遷移額外班記錄資料...');
        
        for (const record of records) {
            try {
                // 轉換額外班記錄資料格式
                const migratedRecord = this.transformOvertimeRecordData(record);
                
                // 發送到 Google Sheets
                await window.workScheduleAPI.createOvertimeRecord(migratedRecord);
                
                this.migrationStatus.completed++;
                console.log(`✓ 額外班記錄遷移完成: ${record.id}`);
                
            } catch (error) {
                console.error(`✗ 額外班記錄遷移失敗: ${record.id}`, error);
                this.migrationStatus.errors.push({
                    type: 'OVERTIME_RECORD_MIGRATION_FAILED',
                    recordId: record.id,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    }

    /**
     * 遷移通知資料
     */
    async migrateNotifications(notifications) {
        console.log('🔔 開始遷移通知資料...');
        
        for (const notification of notifications) {
            try {
                // 轉換通知資料格式
                const migratedNotification = this.transformNotificationData(notification);
                
                // 發送到 Google Sheets
                await window.workScheduleAPI.createNotification(migratedNotification);
                
                this.migrationStatus.completed++;
                console.log(`✓ 通知遷移完成: ${notification.id}`);
                
            } catch (error) {
                console.error(`✗ 通知遷移失敗: ${notification.id}`, error);
                this.migrationStatus.errors.push({
                    type: 'NOTIFICATION_MIGRATION_FAILED',
                    notificationId: notification.id,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    }

    /**
     * 遷移系統設定
     */
    async migrateSystemSettings(settings) {
        console.log('⚙️ 開始遷移系統設定...');
        
        try {
            // 轉換系統設定格式
            const migratedSettings = this.transformSystemSettingsData(settings);
            
            // 發送到 Google Sheets
            await window.workScheduleAPI.updateSystemSettings(migratedSettings);
            
            this.migrationStatus.completed++;
            console.log('✓ 系統設定遷移完成');
            
        } catch (error) {
            console.error('✗ 系統設定遷移失敗', error);
            this.migrationStatus.errors.push({
                type: 'SYSTEM_SETTINGS_MIGRATION_FAILED',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    // ==================== 資料轉換函數 ====================

    /**
     * 轉換用戶資料格式
     */
    transformUserData(user) {
        return {
            id: user.id,
            name: user.name,
            email: user.email,
            password: user.password,
            role: user.role,
            location: user.location || 'ciguang',
            department: user.department || '',
            lineId: user.lineId || '',
            approved: user.approved !== false, // 預設為 true
            monthlyLeaveDays: user.monthlyLeaveDays || (user.role === 'employee' ? 8 : 6),
            overtimeBalance: user.overtimeBalance || 0,
            annualSickLeaveQuota: user.annualSickLeaveQuota || (user.role === 'employee' ? 30 : 20),
            sickLeaveBalance: user.sickLeaveBalance || user.annualSickLeaveQuota || (user.role === 'employee' ? 30 : 20),
            usedSickLeaveDays: user.usedSickLeaveDays || 0,
            weeklySchedule: user.weeklySchedule || {},
            registrationDate: user.registrationDate || new Date().toISOString(),
            lastLoginDate: user.lastLoginDate || '',
            isActive: user.isActive !== false // 預設為 true
        };
    }

    /**
     * 轉換排班申請資料格式
     */
    transformScheduleRequestData(request) {
        return {
            id: request.id,
            employeeId: request.employeeId,
            employeeName: request.employeeName,
            month: request.month,
            status: request.status || 'pending',
            scheduleData: request.scheduleData || {},
            hasCompensatoryLeave: request.hasCompensatoryLeave || false,
            compensatoryDaysCount: request.compensatoryDaysCount || 0,
            hasSickLeave: request.hasSickLeave || false,
            sickLeaveDaysCount: request.sickLeaveDaysCount || 0,
            submittedAt: request.submittedAt || new Date().toISOString(),
            approver: request.approver || '',
            approvedAt: request.approvedAt || '',
            comments: request.comments || '',
            originalCompensatoryDays: request.originalCompensatoryDays || 0,
            originalSickLeaveDays: request.originalSickLeaveDays || 0
        };
    }

    /**
     * 轉換額外班記錄資料格式
     */
    transformOvertimeRecordData(record) {
        return {
            id: record.id,
            employeeId: record.employeeId,
            employeeName: record.employeeName,
            date: record.date,
            duration: record.duration || 1,
            type: record.type || 'overtime',
            addedBy: record.addedBy || 'system',
            addedDate: record.addedDate || new Date().toISOString(),
            reason: record.reason || '',
            isApproved: record.isApproved !== false // 預設為 true
        };
    }

    /**
     * 轉換通知資料格式
     */
    transformNotificationData(notification) {
        return {
            id: notification.id,
            type: notification.type || 'general',
            title: notification.title,
            message: notification.message,
            targetUserId: notification.targetUserId,
            isRead: notification.isRead || false,
            createdAt: notification.createdAt || new Date().toISOString(),
            createdBy: notification.createdBy || 'system',
            priority: notification.priority || 'medium',
            category: notification.category || 'general'
        };
    }

    /**
     * 轉換系統設定資料格式
     */
    transformSystemSettingsData(settings) {
        return {
            companyName: settings.companyName || '工作排班管理系統',
            workLocations: settings.workLocations || [
                { id: 'ciguang', name: '慈光', minStaff: 2, address: '台北市慈光路123號' },
                { id: 'ruiguang', name: '瑞光', minStaff: 1, address: '台北市瑞光路456號' }
            ],
            shiftTypes: settings.shiftTypes || [
                { id: 'early', name: '早班', startTime: '09:00', endTime: '17:00', duration: 8 },
                { id: 'mid', name: '中班', startTime: '13:00', endTime: '21:00', duration: 8 }
            ],
            departments: settings.departments || [],
            registration: settings.registration || {
                enabled: true,
                requiresApproval: true,
                allowedDomains: [],
                defaultRole: 'employee',
                autoApproveAdmins: false,
                showRegistrationLink: true,
                registrationMessage: '請聯繫系統管理員建立帳戶'
            },
            systemParams: settings.systemParams || {
                maxMonthlyLeaveDays: 15,
                defaultSickLeaveQuota: 30,
                maxOvertimeBalance: 50,
                advanceNotificationDays: 1,
                autoApprovalEnabled: false
            },
            notificationSettings: settings.notificationSettings || {},
            notificationChannels: settings.notificationChannels || {},
            lineSettings: settings.lineSettings || {}
        };
    }

    /**
     * 驗證遷移結果
     */
    async validateMigration() {
        console.log('🔍 驗證遷移結果...');
        
        try {
            // 獲取遷移後的資料
            const migratedUsers = await window.workScheduleAPI.getUsers();
            const migratedRequests = await window.workScheduleAPI.getScheduleRequests();
            const migratedSettings = await window.workScheduleAPI.getSystemSettings();
            
            console.log('✓ 遷移驗證完成:', {
                用戶數: migratedUsers.length,
                排班申請數: migratedRequests.length,
                系統設定: migratedSettings ? '已遷移' : '未遷移'
            });
            
        } catch (error) {
            console.warn('遷移驗證失敗:', error);
            this.migrationStatus.errors.push({
                type: 'VALIDATION_FAILED',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 獲取遷移狀態
     */
    getStatus() {
        return this.migrationStatus;
    }

    /**
     * 生成遷移報告
     */
    generateReport() {
        const duration = this.migrationStatus.endTime - this.migrationStatus.startTime;
        
        return {
            summary: {
                總項目數: this.migrationStatus.total,
                已完成項目: this.migrationStatus.completed,
                成功率: `${Math.round((this.migrationStatus.completed / this.migrationStatus.total) * 100)}%`,
                錯誤數: this.migrationStatus.errors.length,
                耗時: `${Math.round(duration / 1000)}秒`
            },
            errors: this.migrationStatus.errors,
            startTime: this.migrationStatus.startTime,
            endTime: this.migrationStatus.endTime
        };
    }
}

// ==================== 全域遷移實例 ====================

window.dataMigration = new DataMigration();

// ==================== 遷移 UI 函數 ====================

/**
 * 顯示遷移確認對話框
 */
function showMigrationConfirmDialog() {
    const localData = window.dataMigration.loadLocalStorageData();
    
    if (!localData) {
        alert('未找到本地資料，無需遷移。');
        return;
    }
    
    const itemCount = 
        (localData.users?.length || 0) +
        (localData.scheduleRequests?.length || 0) +
        (localData.overtimeRecords?.length || 0) +
        (localData.notifications?.length || 0);
    
    const message = `
確定要開始資料遷移嗎？

將遷移以下資料到 Google Sheets：
• 用戶資料：${localData.users?.length || 0} 筆
• 排班申請：${localData.scheduleRequests?.length || 0} 筆
• 額外班記錄：${localData.overtimeRecords?.length || 0} 筆
• 通知記錄：${localData.notifications?.length || 0} 筆
• 系統設定：1 筆

總計：${itemCount + 1} 筆資料

注意：
1. 遷移過程中請勿關閉瀏覽器
2. 本地資料將自動備份
3. 遷移完成後建議驗證資料完整性
    `;
    
    if (confirm(message)) {
        startDataMigration();
    }
}

/**
 * 開始資料遷移
 */
async function startDataMigration() {
    try {
        showLoading('正在遷移資料，請稍候...');
        
        const result = await window.dataMigration.startMigration();
        
        hideLoading();
        
        if (result.success) {
            const report = window.dataMigration.generateReport();
            showMigrationSuccessDialog(report);
        } else {
            showMigrationErrorDialog(result);
        }
        
    } catch (error) {
        hideLoading();
        showError(`遷移過程中發生錯誤: ${error.message}`);
    }
}

/**
 * 顯示遷移成功對話框
 */
function showMigrationSuccessDialog(report) {
    const message = `
🎉 資料遷移成功完成！

遷移摘要：
• 總項目數：${report.summary.總項目數}
• 已完成：${report.summary.已完成項目}
• 成功率：${report.summary.成功率}
• 耗時：${report.summary.耗時}

${report.summary.錯誤數 > 0 ? `⚠️ 發生 ${report.summary.錯誤數} 個錯誤，請檢查控制台日誌。` : ''}

現在您可以：
1. 重新載入頁面體驗雲端版本
2. 檢查資料完整性
3. 開始使用多人協作功能
    `;
    
    alert(message);
    
    // 詢問是否重新載入頁面
    if (confirm('是否要重新載入頁面以使用雲端版本？')) {
        location.reload();
    }
}

/**
 * 顯示遷移錯誤對話框
 */
function showMigrationErrorDialog(result) {
    const message = `
❌ 資料遷移失敗

錯誤訊息：${result.message}

已完成項目：${result.status.completed}/${result.status.total}
錯誤數：${result.status.errors.length}

請檢查：
1. 網路連線是否正常
2. Google Apps Script URL 是否正確
3. 是否有足夠的權限

詳細錯誤資訊請查看控制台日誌。
    `;
    
    alert(message);
}
