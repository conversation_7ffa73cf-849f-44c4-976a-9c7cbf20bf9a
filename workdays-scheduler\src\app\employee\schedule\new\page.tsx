'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Clock, MapPin, Save, Send, ArrowLeft, AlertCircle } from 'lucide-react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'employee';
  location: 'ciguang' | 'ruiguang';
  approved: boolean;
}

interface ShiftRequest {
  date: string;
  type: 'early' | 'mid' | 'flex' | 'custom' | 'off';
  startTime?: string;
  endTime?: string;
  location: 'ciguang' | 'ruiguang';
  note?: string;
}

export default function NewScheduleRequest() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [selectedMonth, setSelectedMonth] = useState('');
  const [shifts, setShifts] = useState<ShiftRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    // 檢查用戶登入狀態
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== 'employee') {
      router.push('/login');
      return;
    }

    setUser(parsedUser);
    
    // 設定預設月份為下個月
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const monthString = nextMonth.toISOString().slice(0, 7);
    setSelectedMonth(monthString);
    generateMonthDays(monthString, parsedUser.location);
  }, [router]);

  const generateMonthDays = (month: string, defaultLocation: 'ciguang' | 'ruiguang') => {
    const year = parseInt(month.split('-')[0]);
    const monthNum = parseInt(month.split('-')[1]);
    const daysInMonth = new Date(year, monthNum, 0).getDate();
    
    const newShifts: ShiftRequest[] = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const date = `${year}-${monthNum.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      const dayOfWeek = new Date(date).getDay();
      
      // 週末預設為休假
      const defaultType = (dayOfWeek === 0 || dayOfWeek === 6) ? 'off' : 'early';
      
      newShifts.push({
        date,
        type: defaultType,
        location: defaultLocation
      });
    }
    setShifts(newShifts);
  };

  const handleMonthChange = (month: string) => {
    setSelectedMonth(month);
    if (user) {
      generateMonthDays(month, user.location);
    }
  };

  const updateShift = (index: number, updates: Partial<ShiftRequest>) => {
    const newShifts = [...shifts];
    newShifts[index] = { ...newShifts[index], ...updates };
    setShifts(newShifts);
  };

  const getShiftTypeText = (type: string) => {
    switch (type) {
      case 'early': return '早班 (9:00-17:00)';
      case 'mid': return '中班 (13:00-21:00)';
      case 'flex': return '彈性班';
      case 'custom': return '自訂班次';
      case 'off': return '休假';
      default: return type;
    }
  };

  const validateSchedule = () => {
    const workDays = shifts.filter(s => s.type !== 'off');
    
    // 檢查是否有足夠的工作天數
    if (workDays.length < 15) {
      return '每月至少需要15個工作天';
    }

    // 檢查彈性班和自訂班次是否有設定時間
    for (const shift of workDays) {
      if ((shift.type === 'flex' || shift.type === 'custom') && (!shift.startTime || !shift.endTime)) {
        return `${shift.date} 的${getShiftTypeText(shift.type)}需要設定開始和結束時間`;
      }
    }

    return null;
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // 模擬儲存草稿
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess('草稿已儲存');
    } catch (error) {
      setError('儲存失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    const validationError = validateSchedule();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // 模擬提交申請
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSuccess('排班申請已提交，等待主管審核');
      
      // 3秒後返回儀表板
      setTimeout(() => {
        router.push('/employee/dashboard');
      }, 3000);
    } catch (error) {
      setError('提交失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const getDayOfWeekText = (date: string) => {
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return days[new Date(date).getDay()];
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/employee/dashboard')}
                className="mr-4 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-6 w-6" />
              </button>
              <Calendar className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">新增排班申請</h1>
                <p className="text-sm text-gray-600">{user.name} - {user.location === 'ciguang' ? '慈光' : '瑞光'}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Month Selection */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">選擇申請月份</h2>
            <div className="flex items-center space-x-4">
              <label htmlFor="month" className="text-sm font-medium text-gray-700">
                申請月份：
              </label>
              <input
                type="month"
                id="month"
                value={selectedMonth}
                onChange={(e) => handleMonthChange(e.target.value)}
                min={new Date().toISOString().slice(0, 7)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>

          {/* Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6 flex items-center">
              <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
              <span className="text-sm text-green-700">{success}</span>
            </div>
          )}

          {/* Schedule Grid */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">排班設定</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {shifts.map((shift, index) => (
                <div key={shift.date} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-900">
                      {new Date(shift.date).getDate()}日 ({getDayOfWeekText(shift.date)})
                    </span>
                    <span className="text-xs text-gray-500">{shift.date}</span>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">班次類型</label>
                      <select
                        value={shift.type}
                        onChange={(e) => updateShift(index, { type: e.target.value as any })}
                        className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      >
                        <option value="early">早班 (9:00-17:00)</option>
                        <option value="mid">中班 (13:00-21:00)</option>
                        <option value="flex">彈性班</option>
                        <option value="custom">自訂班次</option>
                        <option value="off">休假</option>
                      </select>
                    </div>

                    {(shift.type === 'flex' || shift.type === 'custom') && (
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">開始</label>
                          <input
                            type="time"
                            value={shift.startTime || ''}
                            onChange={(e) => updateShift(index, { startTime: e.target.value })}
                            className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">結束</label>
                          <input
                            type="time"
                            value={shift.endTime || ''}
                            onChange={(e) => updateShift(index, { endTime: e.target.value })}
                            className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                          />
                        </div>
                      </div>
                    )}

                    {shift.type !== 'off' && (
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">地點</label>
                        <select
                          value={shift.location}
                          onChange={(e) => updateShift(index, { location: e.target.value as any })}
                          className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          <option value="ciguang">慈光</option>
                          <option value="ruiguang">瑞光</option>
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={handleSaveDraft}
              disabled={loading}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <Save className="h-4 w-4 mr-2" />
              儲存草稿
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <Send className="h-4 w-4 mr-2" />
              {loading ? '提交中...' : '提交申請'}
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
