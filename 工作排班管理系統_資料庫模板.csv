# 工作排班管理系統 - Google Sheets 資料庫模板

## 說明
此檔案包含建立 Google Sheets 資料庫所需的所有工作表結構。
請按照以下步驟操作：

1. 將此檔案上傳到您的 Google Drive
2. 轉換為 Google Sheets
3. 按照下方結構建立各個工作表

## 工作表1: Users (用戶資料)
id,name,email,password,role,location,department,lineId,approved,monthlyLeaveDays,overtimeBalance,annualSickLeaveQuota,sickLeaveBalance,usedSickLeaveDays,weeklySchedule,registrationDate,lastLoginDate,isActive

## 工作表2: ScheduleRequests (排班申請)
id,employeeId,employeeName,month,status,scheduleData,hasCompensatoryLeave,compensatoryDaysCount,hasSickLeave,sickLeaveDaysCount,submittedAt,approver,approvedAt,comments,originalCompensatoryDays,originalSickLeaveDays

## 工作表3: OvertimeRecords (額外班記錄)
id,employeeId,employeeName,date,duration,type,addedBy,addedDate,reason,isApproved

## 工作表4: Notifications (通知記錄)
id,type,title,message,targetUserId,isRead,createdAt,createdBy,priority,category

## 工作表5: EmployeeSchedules (員工排班)
id,employeeId,employeeName,month,scheduleData,totalWorkDays,totalLeaveDays,createdAt,updatedAt

## 工作表6: ShiftChanges (調班記錄)
id,employeeId,employeeName,originalDate,newDate,originalShift,newShift,reason,status,requestedAt,approver,approvedAt

## 工作表7: SystemSettings (系統設定)
settingKey,settingValue,category,description,updatedAt,updatedBy

## 工作表8: ActivityLogs (操作日誌)
id,userId,userName,action,targetType,targetId,details,ipAddress,userAgent,timestamp
