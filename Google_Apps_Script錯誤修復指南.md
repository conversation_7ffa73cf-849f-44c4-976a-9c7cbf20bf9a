# Google Apps Script 錯誤修復指南

## 🚨 錯誤分析

您遇到的錯誤：`TypeError: Cannot read properties of undefined (reading 'postData')`

### **錯誤原因**
1. **請求格式問題**：前端發送的請求格式不正確
2. **Apps Script 設定問題**：Web App 部署設定不當
3. **權限問題**：Apps Script 沒有足夠的執行權限

## 🔧 修復步驟

### **步驟 1：更新 Google Apps Script 程式碼**

我已經修復了後端程式碼中的問題：

1. **增強錯誤處理**：檢查 `postData` 是否存在
2. **修復過時方法**：將 `substr()` 改為 `substring()`
3. **添加測試端點**：新增連接測試功能
4. **改善日誌記錄**：更詳細的錯誤資訊

### **步驟 2：重新部署 Google Apps Script**

#### **2.1 更新程式碼**
1. 開啟您的 Google Apps Script 專案
2. 複製修復後的 `Google_Apps_Script_後端.js` 內容
3. 完全替換現有程式碼
4. **重要**：修改第 10 行的 `SPREADSHEET_ID`：
   ```javascript
   const SPREADSHEET_ID = '您的實際_SHEETS_ID';
   ```

#### **2.2 測試程式碼**
1. 點擊「儲存」（Ctrl+S）
2. 選擇函數：`handleTestConnection`
3. 點擊「執行」測試
4. 檢查執行日誌是否有錯誤

#### **2.3 重新部署 Web App**
1. 點擊「部署」→「管理部署作業」
2. 點擊現有部署旁的「編輯」圖示
3. 更新版本：選擇「新版本」
4. 說明：`修復 postData 錯誤 v1.1`
5. 點擊「部署」
6. **複製新的 Web App URL**

### **步驟 3：測試 API 連接**

#### **3.1 瀏覽器測試**
在瀏覽器中訪問：
```
您的_WEB_APP_URL?action=health
```

**預期回應**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "sheetsId": "您的_SHEETS_ID"
  }
}
```

#### **3.2 連接測試**
在瀏覽器中訪問：
```
您的_WEB_APP_URL?action=test
```

**預期回應**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "message": "Connection test successful",
    "sheetsId": "您的_SHEETS_ID",
    "sheetsName": "工作排班管理系統",
    "sheetsCount": 8,
    "sheets": [...]
  }
}
```

### **步驟 4：檢查常見問題**

#### **4.1 權限問題**
如果遇到權限錯誤：
1. 在 Apps Script 中點擊「執行」
2. 授權所有必要權限
3. 確認可以訪問 Google Sheets

#### **4.2 CORS 問題**
如果前端無法訪問 API：
1. 確認 Web App 設定為「任何人」都可執行
2. 檢查前端是否使用 HTTPS

#### **4.3 Sheets ID 問題**
確認 Sheets ID 設定正確：
1. 開啟您的 Google Sheets
2. 從 URL 複製正確的 ID
3. 更新 Apps Script 中的 `SPREADSHEET_ID`

## 🧪 完整測試流程

### **測試 1：基本連接**
```javascript
// 在瀏覽器控制台執行
fetch('您的_WEB_APP_URL?action=health')
  .then(response => response.json())
  .then(data => console.log('Health check:', data))
  .catch(error => console.error('Error:', error));
```

### **測試 2：POST 請求**
```javascript
// 在瀏覽器控制台執行
fetch('您的_WEB_APP_URL', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    action: 'getSystemSettings',
    data: null,
    auth: {
      userId: 'test',
      token: 'test'
    }
  })
})
.then(response => response.json())
.then(data => console.log('POST test:', data))
.catch(error => console.error('POST Error:', error));
```

## 🔍 除錯技巧

### **查看 Apps Script 日誌**
1. 在 Apps Script 編輯器中
2. 點擊左側「執行」圖示
3. 查看執行歷史和錯誤日誌

### **檢查網路請求**
1. 按 F12 開啟開發者工具
2. 切換到「Network」標籤
3. 執行 API 請求
4. 檢查請求和回應詳情

### **常見錯誤代碼**
- `INVALID_REQUEST`：請求格式錯誤
- `INVALID_JSON`：JSON 格式錯誤
- `CONNECTION_FAILED`：無法連接到 Sheets
- `PERMISSION_DENIED`：權限不足

## 📝 部署檢查清單

### **Apps Script 設定**
- [ ] 程式碼已更新為修復版本
- [ ] SPREADSHEET_ID 設定正確
- [ ] 函數可以正常執行
- [ ] 已授權所有必要權限

### **Web App 部署**
- [ ] 已重新部署為新版本
- [ ] 執行身分設為「我」
- [ ] 存取權設為「任何人」
- [ ] 已複製新的 Web App URL

### **連接測試**
- [ ] Health check 回應正常
- [ ] Connection test 回應正常
- [ ] 可以訪問 Google Sheets
- [ ] 前端可以呼叫 API

## 🚀 下一步

完成修復後：

1. **更新前端設定**
   ```javascript
   const GOOGLE_APPS_SCRIPT_URL = '您的新_WEB_APP_URL';
   ```

2. **測試基本功能**
   - 用戶登入
   - 資料讀取
   - 資料寫入

3. **執行資料遷移**
   - 確認 API 正常運作
   - 執行遷移腳本
   - 驗證遷移結果

## 💡 預防措施

### **避免類似錯誤**
1. **總是檢查參數**：在使用前檢查物件是否存在
2. **使用現代語法**：避免過時的方法
3. **完整測試**：部署前進行完整測試
4. **版本控制**：保留工作版本的備份

### **監控和維護**
1. **定期檢查**：定期測試 API 功能
2. **日誌監控**：檢查 Apps Script 執行日誌
3. **效能監控**：監控回應時間
4. **錯誤處理**：完善的錯誤處理機制

## 📞 需要協助？

如果修復後仍有問題，請提供：

1. **錯誤訊息**：完整的錯誤日誌
2. **測試結果**：Health check 和 Connection test 結果
3. **設定資訊**：Sheets ID 和 Web App URL（可隱藏敏感部分）
4. **操作步驟**：您執行的具體步驟

我會協助您進一步診斷和解決問題！
