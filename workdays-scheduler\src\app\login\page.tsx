'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Calendar, Eye, EyeOff, AlertCircle } from 'lucide-react';

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string;
    role: 'admin' | 'supervisor' | 'employee';
    location: 'ciguang' | 'ruiguang';
    approved: boolean;
  };
  message?: string;
}

export default function LoginPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // 模擬 API 呼叫 - 之後會整合 Google Sheets
      const response = await mockLogin(formData);

      if (response.success && response.user) {
        if (!response.user.approved) {
          setError('您的帳戶尚未通過審核，請聯繫管理員。');
          return;
        }

        // 儲存用戶資訊到 localStorage (之後會改用更安全的方式)
        localStorage.setItem('user', JSON.stringify(response.user));

        // 根據角色導向不同頁面
        switch (response.user.role) {
          case 'admin':
            router.push('/admin/dashboard');
            break;
          case 'supervisor':
            router.push('/supervisor/dashboard');
            break;
          case 'employee':
            router.push('/employee/dashboard');
            break;
          default:
            router.push('/dashboard');
        }
      } else {
        setError(response.message || '登入失敗，請檢查您的帳號密碼。');
      }
    } catch (error) {
      setError('系統錯誤，請稍後再試。');
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 模擬登入 API - 之後會替換為真實的 Google Sheets 整合
  const mockLogin = async (data: LoginFormData): Promise<LoginResponse> => {
    // 模擬網路延遲
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模擬用戶資料 (之後從 Google Sheets 讀取)
    const mockUsers = [
      {
        id: '1',
        email: '<EMAIL>',
        password: 'admin123',
        name: '系統管理員',
        role: 'admin' as const,
        location: 'ciguang' as const,
        approved: true
      },
      {
        id: '2',
        email: '<EMAIL>',
        password: 'super123',
        name: '主管',
        role: 'supervisor' as const,
        location: 'ciguang' as const,
        approved: true
      },
      {
        id: '3',
        email: '<EMAIL>',
        password: 'emp123',
        name: '員工',
        role: 'employee' as const,
        location: 'ruiguang' as const,
        approved: true
      }
    ];

    const user = mockUsers.find(u => u.email === data.email && u.password === data.password);

    if (user) {
      const { password, ...userWithoutPassword } = user;
      return {
        success: true,
        user: userWithoutPassword
      };
    } else {
      return {
        success: false,
        message: '電子郵件或密碼錯誤'
      };
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="flex justify-center">
            <Calendar className="h-12 w-12 text-indigo-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            登入您的帳戶
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            或{' '}
            <Link href="/register" className="font-medium text-indigo-600 hover:text-indigo-500">
              申請新帳戶
            </Link>
          </p>
        </div>

        {/* 測試帳號提示 */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">測試帳號：</h3>
          <div className="text-xs text-blue-700 space-y-1">
            <div>管理員: <EMAIL> / admin123</div>
            <div>主管: <EMAIL> / super123</div>
            <div>員工: <EMAIL> / emp123</div>
          </div>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-center">
              <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                電子郵件
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                disabled={loading}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm disabled:bg-gray-100"
                placeholder="電子郵件地址"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              />
            </div>
            <div className="relative">
              <label htmlFor="password" className="sr-only">
                密碼
              </label>
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                disabled={loading}
                className="appearance-none rounded-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm disabled:bg-gray-100"
                placeholder="密碼"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                ) : (
                  <Eye className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                disabled={loading}
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                記住我
              </label>
            </div>

            <div className="text-sm">
              <a href="#" className="font-medium text-indigo-600 hover:text-indigo-500">
                忘記密碼？
              </a>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? '登入中...' : '登入'}
            </button>
          </div>

          <div className="text-center">
            <Link href="/" className="text-indigo-600 hover:text-indigo-500 text-sm">
              ← 返回首頁
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
