'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Users, Settings, FileText, LogOut, Shield, Database, Bell, BarChart3 } from 'lucide-react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'employee';
  location: 'ciguang' | 'ruiguang';
  approved: boolean;
}

interface SystemStats {
  totalUsers: number;
  pendingRegistrations: number;
  activeSchedules: number;
  systemHealth: 'good' | 'warning' | 'error';
  lastBackup: string;
  monthlyRequests: number;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [stats, setStats] = useState<SystemStats>({
    totalUsers: 0,
    pendingRegistrations: 0,
    activeSchedules: 0,
    systemHealth: 'good',
    lastBackup: '',
    monthlyRequests: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 檢查用戶登入狀態
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== 'admin') {
      router.push('/login');
      return;
    }

    setUser(parsedUser);
    loadSystemStats();
  }, [router]);

  const loadSystemStats = async () => {
    setLoading(true);
    try {
      // 模擬載入系統統計資料
      const mockStats: SystemStats = {
        totalUsers: 15,
        pendingRegistrations: 3,
        activeSchedules: 8,
        systemHealth: 'good',
        lastBackup: '2024-12-10T10:30:00Z',
        monthlyRequests: 45
      };

      setStats(mockStats);
    } catch (error) {
      console.error('載入系統統計失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    router.push('/');
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'good': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthText = (health: string) => {
    switch (health) {
      case 'good': return '正常';
      case 'warning': return '警告';
      case 'error': return '錯誤';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">系統管理中心</h1>
                <p className="text-sm text-gray-600">歡迎回來，{user?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">管理員</span>
              <button
                onClick={handleLogout}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <LogOut className="h-5 w-5 mr-1" />
                登出
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* System Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-blue-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">總用戶數</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalUsers}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Bell className="h-8 w-8 text-yellow-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">待審核註冊</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.pendingRegistrations}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Calendar className="h-8 w-8 text-green-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">活躍排班</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.activeSchedules}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Database className="h-8 w-8 text-purple-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">系統狀態</dt>
                      <dd className="flex items-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getHealthColor(stats.systemHealth)}`}>
                          {getHealthText(stats.systemHealth)}
                        </span>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BarChart3 className="h-8 w-8 text-indigo-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">本月申請數</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.monthlyRequests}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Database className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">最後備份</dt>
                      <dd className="text-sm font-medium text-gray-900">
                        {new Date(stats.lastBackup).toLocaleDateString('zh-TW')}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Management Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <button
              onClick={() => router.push('/admin/users')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">用戶管理</h3>
              <p className="text-sm text-gray-600 text-center mt-1">管理用戶帳戶與權限</p>
            </button>

            <button
              onClick={() => router.push('/admin/system-settings')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <Settings className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">系統設定</h3>
              <p className="text-sm text-gray-600 text-center mt-1">配置系統參數</p>
            </button>

            <button
              onClick={() => router.push('/admin/reports')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">系統報表</h3>
              <p className="text-sm text-gray-600 text-center mt-1">查看系統使用統計</p>
            </button>

            <button
              onClick={() => router.push('/admin/backup')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <Database className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">資料備份</h3>
              <p className="text-sm text-gray-600 text-center mt-1">管理資料備份與還原</p>
            </button>
          </div>

          {/* Recent Activity */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">最近活動</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-5 w-5 text-blue-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-gray-900">新用戶註冊：李小華 (瑞光)</p>
                    <p className="text-xs text-gray-500">2 小時前</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Calendar className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-gray-900">排班申請已核准：張小明 (慈光)</p>
                    <p className="text-xs text-gray-500">4 小時前</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Database className="h-5 w-5 text-purple-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-gray-900">系統自動備份完成</p>
                    <p className="text-xs text-gray-500">6 小時前</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
