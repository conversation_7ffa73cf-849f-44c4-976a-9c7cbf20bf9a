# Google Sheets 資料庫設計文檔

## 📊 資料庫架構概覽

### **工作簿名稱**：工作排班管理系統
### **工作表結構**：

```
工作排班管理系統.xlsx
├── 📋 Users（用戶資料）
├── 📅 ScheduleRequests（排班申請）
├── ⏰ OvertimeRecords（額外班記錄）
├── 🔔 Notifications（通知記錄）
├── 📊 EmployeeSchedules（員工排班）
├── 🔄 ShiftChanges（調班記錄）
├── ⚙️ SystemSettings（系統設定）
└── 📝 ActivityLogs（操作日誌）
```

## 📋 詳細工作表設計

### **1. Users（用戶資料表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 用戶唯一識別碼 | user_001 |
| B: name | 文字 | 用戶姓名 | 張三 |
| C: email | 文字 | 電子郵件 | <EMAIL> |
| D: password | 文字 | 密碼（加密） | ******** |
| E: role | 文字 | 角色 | employee/supervisor/admin |
| F: location | 文字 | 工作地點 | ciguang/ruiguang |
| G: department | 文字 | 部門ID | admin/operations |
| H: lineId | 文字 | LINE ID | U1234567890 |
| I: approved | 布林 | 是否已審核 | TRUE/FALSE |
| J: monthlyLeaveDays | 數字 | 月休天數 | 8 |
| K: overtimeBalance | 數字 | 額外班餘額 | 5 |
| L: annualSickLeaveQuota | 數字 | 年度事病假額度 | 30 |
| M: sickLeaveBalance | 數字 | 事病假餘額 | 25 |
| N: usedSickLeaveDays | 數字 | 已使用事病假 | 5 |
| O: weeklySchedule | JSON | 週班表設定 | {"monday":{"type":"early"}...} |
| P: registrationDate | 日期時間 | 註冊日期 | 2024-12-01T10:00:00Z |
| Q: lastLoginDate | 日期時間 | 最後登入 | 2024-12-01T15:30:00Z |
| R: isActive | 布林 | 是否啟用 | TRUE/FALSE |

### **2. ScheduleRequests（排班申請表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 申請唯一識別碼 | req_001 |
| B: employeeId | 文字 | 員工ID | user_001 |
| C: employeeName | 文字 | 員工姓名 | 張三 |
| D: month | 文字 | 申請月份 | 2024-12 |
| E: status | 文字 | 狀態 | pending/approved/rejected |
| F: scheduleData | JSON | 排班資料 | {"1":{"shift":"early","location":"ciguang"}...} |
| G: hasCompensatoryLeave | 布林 | 是否有補休 | TRUE/FALSE |
| H: compensatoryDaysCount | 數字 | 補休天數 | 2 |
| I: hasSickLeave | 布林 | 是否有事病假 | TRUE/FALSE |
| J: sickLeaveDaysCount | 數字 | 事病假天數 | 1 |
| K: submittedAt | 日期時間 | 提交時間 | 2024-12-01T10:00:00Z |
| L: approver | 文字 | 審核人 | 李主管 |
| M: approvedAt | 日期時間 | 審核時間 | 2024-12-01T15:00:00Z |
| N: comments | 文字 | 審核備註 | 已核准 |
| O: originalCompensatoryDays | 數字 | 原始補休天數 | 0 |
| P: originalSickLeaveDays | 數字 | 原始事病假天數 | 0 |

### **3. OvertimeRecords（額外班記錄表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 記錄唯一識別碼 | ot_001 |
| B: employeeId | 文字 | 員工ID | user_001 |
| C: employeeName | 文字 | 員工姓名 | 張三 |
| D: date | 日期 | 額外班日期 | 2024-12-01 |
| E: duration | 數字 | 時數（天） | 0.5/1 |
| F: type | 文字 | 類型 | overtime/compensatory |
| G: addedBy | 文字 | 加註人 | admin_001 |
| H: addedDate | 日期時間 | 加註時間 | 2024-12-01T16:00:00Z |
| I: reason | 文字 | 原因 | 加班工作 |
| J: isApproved | 布林 | 是否已核准 | TRUE/FALSE |

### **4. Notifications（通知記錄表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 通知唯一識別碼 | notif_001 |
| B: type | 文字 | 通知類型 | schedule_submission |
| C: title | 文字 | 通知標題 | 排班申請提醒 |
| D: message | 文字 | 通知內容 | 您有新的排班申請需要審核 |
| E: targetUserId | 文字 | 目標用戶ID | user_001 |
| F: isRead | 布林 | 是否已讀 | TRUE/FALSE |
| G: createdAt | 日期時間 | 建立時間 | 2024-12-01T10:00:00Z |
| H: createdBy | 文字 | 建立者 | system |
| I: priority | 文字 | 優先級 | high/medium/low |
| J: category | 文字 | 分類 | schedule/overtime/system |

### **5. EmployeeSchedules（員工排班表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 排班唯一識別碼 | sch_001 |
| B: employeeId | 文字 | 員工ID | user_001 |
| C: employeeName | 文字 | 員工姓名 | 張三 |
| D: month | 文字 | 月份 | 2024-12 |
| E: scheduleData | JSON | 排班資料 | {"1":{"shift":"early","location":"ciguang"}...} |
| F: totalWorkDays | 數字 | 總工作天數 | 22 |
| G: totalLeaveDays | 數字 | 總休假天數 | 8 |
| H: createdAt | 日期時間 | 建立時間 | 2024-12-01T10:00:00Z |
| I: updatedAt | 日期時間 | 更新時間 | 2024-12-01T15:00:00Z |

### **6. ShiftChanges（調班記錄表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 調班唯一識別碼 | change_001 |
| B: employeeId | 文字 | 員工ID | user_001 |
| C: employeeName | 文字 | 員工姓名 | 張三 |
| D: originalDate | 日期 | 原始日期 | 2024-12-01 |
| E: newDate | 日期 | 新日期 | 2024-12-02 |
| F: originalShift | 文字 | 原始班別 | early |
| G: newShift | 文字 | 新班別 | mid |
| H: reason | 文字 | 調班原因 | 個人事務 |
| I: status | 文字 | 狀態 | pending/approved/rejected |
| J: requestedAt | 日期時間 | 申請時間 | 2024-12-01T10:00:00Z |
| K: approver | 文字 | 審核人 | 李主管 |
| L: approvedAt | 日期時間 | 審核時間 | 2024-12-01T15:00:00Z |

### **7. SystemSettings（系統設定表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: settingKey | 文字 | 設定鍵值 | companyName |
| B: settingValue | JSON | 設定值 | "工作排班管理系統" |
| C: category | 文字 | 分類 | basic/notification/registration |
| D: description | 文字 | 說明 | 公司名稱設定 |
| E: updatedAt | 日期時間 | 更新時間 | 2024-12-01T10:00:00Z |
| F: updatedBy | 文字 | 更新者 | admin_001 |

### **8. ActivityLogs（操作日誌表）**

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| A: id | 文字 | 日誌唯一識別碼 | log_001 |
| B: userId | 文字 | 操作用戶ID | user_001 |
| C: userName | 文字 | 操作用戶名稱 | 張三 |
| D: action | 文字 | 操作動作 | create_schedule_request |
| E: targetType | 文字 | 目標類型 | schedule_request |
| F: targetId | 文字 | 目標ID | req_001 |
| G: details | JSON | 操作詳情 | {"month":"2024-12","status":"submitted"} |
| H: ipAddress | 文字 | IP地址 | ************* |
| I: userAgent | 文字 | 瀏覽器資訊 | Chrome/120.0.0.0 |
| J: timestamp | 日期時間 | 操作時間 | 2024-12-01T10:00:00Z |

## 🔧 資料關聯設計

### **主要關聯**
1. **Users ↔ ScheduleRequests**：一對多（一個用戶多個申請）
2. **Users ↔ OvertimeRecords**：一對多（一個用戶多個額外班記錄）
3. **Users ↔ Notifications**：一對多（一個用戶多個通知）
4. **Users ↔ EmployeeSchedules**：一對多（一個用戶多個月份排班）
5. **ScheduleRequests ↔ EmployeeSchedules**：一對一（申請核准後生成排班）

### **索引設計**
- **Users表**：email（唯一索引）、role、department
- **ScheduleRequests表**：employeeId、month、status
- **OvertimeRecords表**：employeeId、date
- **Notifications表**：targetUserId、isRead、createdAt

## 📝 資料遷移計劃

### **從 localStorage 到 Google Sheets**
1. **匯出現有資料**：從 localStorage 匯出 JSON 格式
2. **資料轉換**：將 JSON 轉換為 Google Sheets 格式
3. **批量匯入**：使用 Google Sheets API 批量寫入
4. **資料驗證**：確認資料完整性和正確性

### **遷移腳本設計**
```javascript
// 資料遷移函數
async function migrateFromLocalStorage() {
    const localData = JSON.parse(localStorage.getItem('workdaysSystemData'));
    
    // 遷移用戶資料
    await migrateUsers(localData.users);
    
    // 遷移排班申請
    await migrateScheduleRequests(localData.scheduleRequests);
    
    // 遷移其他資料...
}
```

## 🔒 安全性考量

### **資料保護**
- **權限控制**：基於角色的資料訪問控制
- **資料加密**：敏感資料（如密碼）加密儲存
- **操作日誌**：記錄所有資料變更操作
- **備份機制**：定期自動備份重要資料

### **API 安全**
- **身份驗證**：每個 API 請求都需要驗證
- **權限檢查**：確保用戶只能訪問授權資料
- **輸入驗證**：防止 SQL 注入和 XSS 攻擊
- **速率限制**：防止 API 濫用

## 📊 效能優化

### **查詢優化**
- **批量操作**：減少 API 呼叫次數
- **資料快取**：快取常用資料
- **分頁載入**：大量資料分頁處理
- **索引使用**：合理使用索引提升查詢效能

### **同步策略**
- **即時同步**：關鍵操作即時同步
- **批量同步**：非關鍵資料批量同步
- **衝突解決**：處理多用戶同時編輯衝突
- **離線支援**：支援離線操作和後續同步

這個設計為我們的 Google Sheets 升級提供了完整的資料庫架構基礎。接下來我將建立 Google Apps Script 後端來實現這個設計。
