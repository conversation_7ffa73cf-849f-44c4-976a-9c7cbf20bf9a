# Google Apps Script 部署指南

## 🚨 錯誤解決：Invalid request: missing postData

這個錯誤通常發生在以下情況：

### **問題原因**
1. **直接在瀏覽器訪問 Web App URL**（發送 GET 而非 POST 請求）
2. **Web App 部署設定錯誤**
3. **SPREADSHEET_ID 未正確設定**

## 📋 正確部署步驟

### **步驟1：建立 Google Sheets**

1. **建立新的 Google Sheets**
   - 前往 [Google Sheets](https://sheets.google.com)
   - 建立新的試算表
   - 命名為「工作排班管理系統」

2. **複製 Sheets ID**
   - 從 URL 複製 Sheets ID
   - 例如：`https://docs.google.com/spreadsheets/d/1ABC...XYZ/edit`
   - Sheets ID 就是 `1ABC...XYZ` 這部分

### **步驟2：設定 Google Apps Script**

1. **開啟 Apps Script**
   - 在 Google Sheets 中，點選「擴充功能」→「Apps Script」

2. **貼上後端代碼**
   - 刪除預設的 `myFunction()`
   - 貼上 `Google_Apps_Script_後端.js` 的完整內容

3. **設定 SPREADSHEET_ID**
   ```javascript
   // 將第11行的 YOUR_SPREADSHEET_ID_HERE 替換為實際的 Sheets ID
   const SPREADSHEET_ID = '1ABC...XYZ'; // 替換為您的 Sheets ID
   ```

### **步驟3：部署 Web App**

1. **點選「部署」→「新增部署作業」**

2. **設定部署參數**
   - **類型**：選擇「網頁應用程式」
   - **說明**：「工作排班管理系統 API v1.0」
   - **執行身分**：選擇「我」
   - **存取權**：選擇「任何人」

3. **點選「部署」**
   - 系統會要求授權，點選「授權存取權」
   - 完成授權流程

4. **複製 Web App URL**
   - 部署完成後會顯示 Web App URL
   - 格式類似：`https://script.google.com/macros/s/ABC.../exec`

## 🧪 測試部署

### **測試1：健康檢查（GET 請求）**

在瀏覽器中訪問：
```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec?action=health
```

**預期回應**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2024-12-...",
    "sheetsId": "YOUR_SHEETS_ID"
  }
}
```

### **測試2：連接測試**

在瀏覽器中訪問：
```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec?action=test
```

**預期回應**：
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "message": "Connection test successful",
    "sheetsId": "YOUR_SHEETS_ID",
    "sheetsName": "工作排班管理系統",
    "sheetsCount": 8,
    "sheets": [...]
  }
}
```

## ⚠️ 常見問題解決

### **問題1：Invalid request: missing postData**
- **原因**：直接在瀏覽器訪問 Web App URL
- **解決**：使用 GET 測試端點（如上述測試1、2）

### **問題2：無法開啟 Google Sheets**
- **原因**：SPREADSHEET_ID 設定錯誤
- **解決**：檢查並更新第11行的 SPREADSHEET_ID

### **問題3：權限錯誤**
- **原因**：未完成授權或權限設定錯誤
- **解決**：重新部署並完成授權流程

### **問題4：工作表不存在**
- **原因**：系統會自動建立所需工作表
- **解決**：執行測試後檢查 Sheets 是否自動建立了工作表

## 📝 POST 請求測試

如果需要測試 POST 請求，可以使用以下工具：

### **使用 curl 測試**
```bash
curl -X POST \
  https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec \
  -H "Content-Type: application/json" \
  -d '{
    "action": "authenticateUser",
    "data": {
      "email": "<EMAIL>",
      "password": "admin123"
    }
  }'
```

### **使用 Postman 測試**
1. **方法**：POST
2. **URL**：您的 Web App URL
3. **Headers**：`Content-Type: application/json`
4. **Body**：選擇 raw JSON
```json
{
  "action": "authenticateUser",
  "data": {
    "email": "<EMAIL>",
    "password": "admin123"
  }
}
```

## ✅ 部署成功確認

如果以下測試都通過，表示部署成功：

1. ✅ 健康檢查返回正確回應
2. ✅ 連接測試顯示工作表資訊
3. ✅ Google Sheets 中自動建立了8個工作表
4. ✅ POST 請求測試成功

## 🔄 更新部署

如果需要更新代碼：

1. **修改 Apps Script 代碼**
2. **點選「部署」→「管理部署作業」**
3. **點選現有部署旁的「編輯」**
4. **更新版本說明**
5. **點選「部署」**

## 📞 問題回報

如果仍有問題，請提供：
1. 完整的錯誤訊息
2. Web App URL（可隱藏敏感部分）
3. 測試步驟和結果
4. Google Sheets 的工作表狀況
