/**
 * 工作排班管理系統 - Google Sheets 快速建立腳本
 * 在 Google Apps Script 中執行此腳本可快速建立所有工作表結構
 */

function createWorkScheduleDatabase() {
  try {
    // 建立新的 Google Sheets
    const spreadsheet = SpreadsheetApp.create('工作排班管理系統');
    
    console.log('✅ 已建立 Google Sheets');
    console.log('📍 Sheets ID: ' + spreadsheet.getId());
    console.log('🔗 Sheets URL: ' + spreadsheet.getUrl());
    
    // 刪除預設工作表
    const defaultSheet = spreadsheet.getSheets()[0];
    
    // 建立所有工作表
    createUsersSheet(spreadsheet);
    createScheduleRequestsSheet(spreadsheet);
    createOvertimeRecordsSheet(spreadsheet);
    createNotificationsSheet(spreadsheet);
    createEmployeeSchedulesSheet(spreadsheet);
    createShiftChangesSheet(spreadsheet);
    createSystemSettingsSheet(spreadsheet);
    createActivityLogsSheet(spreadsheet);
    
    // 刪除預設工作表
    if (defaultSheet.getName() === '工作表1') {
      spreadsheet.deleteSheet(defaultSheet);
    }
    
    // 設定預設工作表為 Users
    const usersSheet = spreadsheet.getSheetByName('Users');
    spreadsheet.setActiveSheet(usersSheet);
    
    console.log('🎉 資料庫建立完成！');
    console.log('📋 請記錄以下資訊：');
    console.log('   Sheets ID: ' + spreadsheet.getId());
    console.log('   Sheets URL: ' + spreadsheet.getUrl());
    
    return {
      success: true,
      sheetsId: spreadsheet.getId(),
      sheetsUrl: spreadsheet.getUrl(),
      message: '資料庫建立成功'
    };
    
  } catch (error) {
    console.error('❌ 建立資料庫失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 建立 Users 工作表
 */
function createUsersSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('Users');
  
  const headers = [
    'id', 'name', 'email', 'password', 'role', 'location', 'department', 'lineId',
    'approved', 'monthlyLeaveDays', 'overtimeBalance', 'annualSickLeaveQuota',
    'sickLeaveBalance', 'usedSickLeaveDays', 'weeklySchedule', 'registrationDate',
    'lastLoginDate', 'isActive'
  ];
  
  setupSheetHeaders(sheet, headers);
  
  // 新增範例管理員資料
  const sampleData = [
    'admin_001', '系統管理員', '<EMAIL>', 'admin123', 'admin', 'ciguang', 'admin', '',
    true, 6, 0, 20, 20, 0, '{}', new Date().toISOString(), '', true
  ];
  
  sheet.getRange(2, 1, 1, headers.length).setValues([sampleData]);
  
  console.log('✅ Users 工作表建立完成');
}

/**
 * 建立 ScheduleRequests 工作表
 */
function createScheduleRequestsSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('ScheduleRequests');
  
  const headers = [
    'id', 'employeeId', 'employeeName', 'month', 'status', 'scheduleData',
    'hasCompensatoryLeave', 'compensatoryDaysCount', 'hasSickLeave', 'sickLeaveDaysCount',
    'submittedAt', 'approver', 'approvedAt', 'comments', 'originalCompensatoryDays',
    'originalSickLeaveDays'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ ScheduleRequests 工作表建立完成');
}

/**
 * 建立 OvertimeRecords 工作表
 */
function createOvertimeRecordsSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('OvertimeRecords');
  
  const headers = [
    'id', 'employeeId', 'employeeName', 'date', 'duration', 'type',
    'addedBy', 'addedDate', 'reason', 'isApproved'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ OvertimeRecords 工作表建立完成');
}

/**
 * 建立 Notifications 工作表
 */
function createNotificationsSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('Notifications');
  
  const headers = [
    'id', 'type', 'title', 'message', 'targetUserId', 'isRead',
    'createdAt', 'createdBy', 'priority', 'category'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ Notifications 工作表建立完成');
}

/**
 * 建立 EmployeeSchedules 工作表
 */
function createEmployeeSchedulesSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('EmployeeSchedules');
  
  const headers = [
    'id', 'employeeId', 'employeeName', 'month', 'scheduleData',
    'totalWorkDays', 'totalLeaveDays', 'createdAt', 'updatedAt'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ EmployeeSchedules 工作表建立完成');
}

/**
 * 建立 ShiftChanges 工作表
 */
function createShiftChangesSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('ShiftChanges');
  
  const headers = [
    'id', 'employeeId', 'employeeName', 'originalDate', 'newDate',
    'originalShift', 'newShift', 'reason', 'status', 'requestedAt',
    'approver', 'approvedAt'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ ShiftChanges 工作表建立完成');
}

/**
 * 建立 SystemSettings 工作表
 */
function createSystemSettingsSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('SystemSettings');
  
  const headers = [
    'settingKey', 'settingValue', 'category', 'description', 'updatedAt', 'updatedBy'
  ];
  
  setupSheetHeaders(sheet, headers);
  
  // 新增預設系統設定
  const defaultSettings = [
    ['companyName', '工作排班管理系統', 'basic', '公司名稱', new Date().toISOString(), 'system'],
    ['workLocations', JSON.stringify([
      { id: 'ciguang', name: '慈光', minStaff: 2, address: '台北市慈光路123號' },
      { id: 'ruiguang', name: '瑞光', minStaff: 1, address: '台北市瑞光路456號' }
    ]), 'basic', '工作地點設定', new Date().toISOString(), 'system'],
    ['shiftTypes', JSON.stringify([
      { id: 'early', name: '早班', startTime: '09:00', endTime: '17:00', duration: 8 },
      { id: 'mid', name: '中班', startTime: '13:00', endTime: '21:00', duration: 8 }
    ]), 'basic', '班別類型設定', new Date().toISOString(), 'system'],
    ['registration', JSON.stringify({
      enabled: true,
      requiresApproval: true,
      allowedDomains: [],
      defaultRole: 'employee',
      autoApproveAdmins: false,
      showRegistrationLink: true,
      registrationMessage: '請聯繫系統管理員建立帳戶'
    }), 'registration', '註冊功能設定', new Date().toISOString(), 'system']
  ];
  
  sheet.getRange(2, 1, defaultSettings.length, headers.length).setValues(defaultSettings);
  
  console.log('✅ SystemSettings 工作表建立完成');
}

/**
 * 建立 ActivityLogs 工作表
 */
function createActivityLogsSheet(spreadsheet) {
  const sheet = spreadsheet.insertSheet('ActivityLogs');
  
  const headers = [
    'id', 'userId', 'userName', 'action', 'targetType', 'targetId',
    'details', 'ipAddress', 'userAgent', 'timestamp'
  ];
  
  setupSheetHeaders(sheet, headers);
  console.log('✅ ActivityLogs 工作表建立完成');
}

/**
 * 設定工作表標題行
 */
function setupSheetHeaders(sheet, headers) {
  // 設定標題行
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // 格式化標題行
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#E3F2FD'); // 淺藍色背景
  headerRange.setHorizontalAlignment('center');
  
  // 凍結標題行
  sheet.setFrozenRows(1);
  
  // 自動調整欄寬
  sheet.autoResizeColumns(1, headers.length);
}

/**
 * 測試函數 - 檢查資料庫結構
 */
function testDatabaseStructure() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sheets = spreadsheet.getSheets();
  
  console.log('📊 資料庫結構檢查：');
  console.log('   Sheets ID: ' + spreadsheet.getId());
  console.log('   工作表數量: ' + sheets.length);
  
  sheets.forEach(sheet => {
    const name = sheet.getName();
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();
    console.log(`   - ${name}: ${lastRow} 行 x ${lastCol} 欄`);
  });
}

/**
 * 使用說明
 */
function showInstructions() {
  console.log(`
🚀 Google Sheets 快速建立腳本使用說明

1. 執行 createWorkScheduleDatabase() 函數建立完整資料庫
2. 執行 testDatabaseStructure() 函數檢查資料庫結構
3. 記錄輸出的 Sheets ID 和 URL

建立完成後您將獲得：
- 完整的 8 個工作表結構
- 格式化的標題行
- 預設的系統設定
- 範例管理員帳戶

注意事項：
- 請確保有建立 Google Sheets 的權限
- 建立後請適當設定共享權限
- 記得保存 Sheets ID 供後續使用
  `);
}

// 自動執行說明
showInstructions();
