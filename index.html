<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理系統</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <i data-lucide="calendar" class="h-8 w-8 text-indigo-600 mr-3"></i>
                        <h1 class="text-2xl font-bold text-gray-900">排班管理系統</h1>
                    </div>
                    <nav class="flex space-x-4">
                        <a href="#login" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">登入</a>
                        <a href="#register" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">註冊</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Hero Section -->
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">智慧排班管理解決方案</h2>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                    簡化排班流程，提升工作效率。支援員工排班申請、主管審批、調班管理等完整功能。
                </p>
                <div class="flex justify-center space-x-4">
                    <a href="#login" class="bg-indigo-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-indigo-700 transition-colors">立即開始</a>
                    <a href="#features" class="border border-indigo-600 text-indigo-600 px-8 py-3 rounded-lg text-lg font-medium hover:bg-indigo-50 transition-colors">了解更多</a>
                </div>
            </div>

            <!-- Features Section -->
            <div id="features" class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <i data-lucide="users" class="h-12 w-12 text-indigo-600 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">多角色管理</h3>
                    <p class="text-gray-600">支援管理員、主管、員工三種角色，權限分明</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <i data-lucide="calendar" class="h-12 w-12 text-indigo-600 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">智慧排班</h3>
                    <p class="text-gray-600">支援多種班次類型，自動檢查人力需求</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <i data-lucide="clock" class="h-12 w-12 text-indigo-600 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">調班申請</h3>
                    <p class="text-gray-600">便捷的調班申請流程，主管線上審批</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <i data-lucide="bar-chart-3" class="h-12 w-12 text-indigo-600 mx-auto mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">數據報表</h3>
                    <p class="text-gray-600">完整的工時統計與排班報表功能</p>
                </div>
            </div>

            <!-- System Info -->
            <div class="bg-white rounded-lg shadow-md p-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">系統功能特色</h3>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">排班管理</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li>• 員工每月提交下月排班需求</li>
                            <li>• 支援早班(9:00-17:00)、中班(13:00-21:00)、彈性班等多種班次</li>
                            <li>• 自動檢查慈光(2人)、瑞光(1人)兩地最低人力需求</li>
                            <li>• 主管線上審批排班申請</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">調班與通知</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li>• 前一天截止的調班申請機制</li>
                            <li>• 郵件與LINE通知功能</li>
                            <li>• 月度與年度工時統計報表</li>
                            <li>• A4格式排班公告表</li>
                            <li>• 需審批註冊，登入後依權限瀏覽</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Technology Stack -->
            <div class="bg-white rounded-lg shadow-md p-8 mt-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">技術架構</h3>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">前端技術</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li>• React + Next.js</li>
                            <li>• TypeScript</li>
                            <li>• Tailwind CSS</li>
                            <li>• 響應式設計</li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">後端服務</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Google Sheets API</li>
                            <li>• Google Apps Script</li>
                            <li>• Gmail API</li>
                            <li>• LINE Notify API</li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">部署方案</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Vercel 免費部署</li>
                            <li>• Google 協作平台</li>
                            <li>• 無需自建伺服器</li>
                            <li>• 自動備份</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="text-center text-gray-600">
                    <p>&copy; 2024 排班管理系統. 版權所有.</p>
                    <p class="mt-2 text-sm">使用 React + Next.js + Google Sheets 建構</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
