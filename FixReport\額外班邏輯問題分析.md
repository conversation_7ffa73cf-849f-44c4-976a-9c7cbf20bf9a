# 額外班邏輯問題深度分析

## 🔍 問題核心

### **主要問題**：假單審批後額外班餘額不正確

### **具體現象**
1. 員工申請包含補休的假單
2. 主管批准申請
3. 員工額外班餘額計算錯誤
4. 重新編輯時餘額顯示不正確

## 📊 當前邏輯流程

### **新申請流程**
```
員工提交申請 → 不調整餘額，只檢查
主管批准 → 扣減額外班餘額
```

### **重新編輯流程**
```
員工選擇已批准月份 → 記錄原始補休天數
員工提交申請 → 記錄到申請資料中
主管批准 → 根據差額調整餘額
```

## 🔧 關鍵函數分析

### **1. processApproval() - 主管審核處理**
**位置**：第2719-2754行
**功能**：處理主管批准/拒絕申請
**問題點**：
- 重新編輯申請的識別邏輯
- 差額計算可能有誤
- 餘額更新時機

### **2. submitScheduleRequest() - 員工提交申請**
**位置**：第2150-2158行
**功能**：員工提交排班申請
**修正**：已移除提交時的餘額處理，只記錄原始資訊

### **3. recalculateEmployeeOvertimeBalance() - 餘額重新計算**
**位置**：第3972-4001行
**功能**：基於記錄重新計算員工額外班餘額
**用途**：確保資料一致性

## 🐛 可能的問題點

### **1. 重新編輯識別錯誤**
```javascript
// 當前邏輯
if (request.originalCompensatoryDays !== undefined) {
    // 重新編輯邏輯
} else {
    // 新申請邏輯
}
```
**問題**：`originalCompensatoryDays` 可能沒有正確設定

### **2. 差額計算錯誤**
```javascript
const difference = newCompensatoryDays - originalCompensatoryDays;
if (difference > 0) {
    // 扣減餘額
} else {
    // 恢復餘額
}
```
**問題**：計算邏輯可能有誤

### **3. 餘額更新時機**
**問題**：可能在錯誤的時機更新餘額

### **4. 資料同步問題**
**問題**：`currentUser.overtimeBalance` 與 `systemData.users[].overtimeBalance` 不同步

## 🧪 測試案例

### **案例1：新申請補休**
```
初始餘額：10天
申請補休：3天
預期結果：批准後餘額變成7天
```

### **案例2：重新編輯增加補休**
```
初始餘額：7天（已有3天補休）
重新編輯：改為5天補休
預期結果：批准後餘額變成5天（扣減2天差額）
```

### **案例3：重新編輯減少補休**
```
初始餘額：5天（已有5天補休）
重新編輯：改為2天補休
預期結果：批准後餘額變成8天（恢復3天差額）
```

### **案例4：完全取消補休**
```
初始餘額：8天（已有2天補休）
重新編輯：改為0天補休
預期結果：批准後餘額變成10天（恢復2天）
```

## 🔍 除錯策略

### **1. Console 輸出檢查**
```javascript
console.log('重新編輯申請處理 - 原始補休:', originalCompensatoryDays);
console.log('新補休:', newCompensatoryDays);
console.log('差額:', difference);
console.log('員工餘額變化:', oldBalance, '→', newBalance);
```

### **2. 資料狀態檢查**
```javascript
debugSystemData(); // 檢查整體資料一致性
```

### **3. 手動驗證**
```javascript
// 檢查特定員工的記錄
systemData.overtimeRecords.filter(r => r.employeeId === '3');
// 檢查員工餘額
systemData.users.find(u => u.id === '3').overtimeBalance;
```

## 🎯 修正方向

### **1. 確保原始資訊正確記錄**
- 檢查 `window.originalCompensatoryDays` 是否正確設定
- 確認 `requestData.originalCompensatoryDays` 正確傳遞

### **2. 驗證差額計算邏輯**
- 確保計算公式正確
- 檢查正負號處理

### **3. 統一餘額更新機制**
- 確保只在一個地方更新餘額
- 同步所有相關變數

### **4. 增強資料驗證**
- 添加餘額計算驗證
- 確保資料一致性

## 📝 建議的修正步驟

### **步驟1：深度除錯**
1. 添加詳細的 Console 輸出
2. 追蹤每個操作的餘額變化
3. 確認資料流程

### **步驟2：邏輯驗證**
1. 檢查重新編輯識別邏輯
2. 驗證差額計算公式
3. 確認餘額更新時機

### **步驟3：測試驗證**
1. 測試所有場景
2. 確認餘額計算正確
3. 驗證資料一致性

### **步驟4：優化改進**
1. 簡化邏輯流程
2. 增強錯誤處理
3. 提升系統穩定性

---

**分析時間**：2024年12月
**狀態**：待新對話中深度修正
