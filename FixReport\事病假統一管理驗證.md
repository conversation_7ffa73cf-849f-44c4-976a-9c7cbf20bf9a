# 事病假統一管理驗證

## 🎯 修正目標
為事病假建立全域統一變數管理，類似額外班餘額的解決方案。

## 🔧 修正內容

### **1. 新增全域變數**
```javascript
let actualSickLeaveBalance = 0; // 當前實際可用的事病假餘額
```

### **2. 新增計算函數**
```javascript
// 計算實際事病假餘額的函數
function calculateActualSickLeaveBalance(employeeId) {
    const user = systemData.users.find(u => u.id === employeeId);
    if (!user) return 0;

    // 年度額度
    const annualQuota = user.annualSickLeaveQuota || 0;
    
    // 總使用 = 所有已批准申請中的事病假天數
    let totalUsed = 0;
    if (systemData.scheduleRequests) {
        systemData.scheduleRequests.forEach(request => {
            if (request.employeeId === employeeId && 
                request.status === 'approved' && 
                request.hasSickLeave) {
                totalUsed += request.sickLeaveDaysCount || 0;
            }
        });
    }

    const actualBalance = annualQuota - totalUsed;
    return actualBalance;
}
```

### **3. 修正餘額顯示函數**
- `updateSickLeaveBalanceDisplay()` 使用 `actualSickLeaveBalance`
- `updateSickLeaveBalanceInSummary()` 使用實際餘額
- `saveDayShift()` 使用實際餘額進行檢查

### **4. 修正審核同步邏輯**
- 批准申請時同步 `actualSickLeaveBalance`
- 確保當前用戶的餘額變數一致

### **5. 新增除錯工具**
```javascript
// 修正員工事病假餘額不一致問題
function fixEmployeeSickLeaveBalance(employeeId)

// 重設所有事病假餘額（除錯用）
function resetAllSickLeaveBalances()
```

## 🧪 測試步驟

### **準備工作**
1. 開啟瀏覽器開發者工具（F12）
2. 切換到 Console 標籤
3. 登入員工帳號：<EMAIL> / emp123

### **測試流程**

#### **步驟1：檢查初始狀態**
```javascript
// 在 Console 中執行
console.log('當前用戶:', currentUser);
console.log('實際額外班餘額:', actualOvertimeBalance);
console.log('實際事病假餘額:', actualSickLeaveBalance);
console.log('年度事病假額度:', currentUser.annualSickLeaveQuota);
console.log('已用事病假:', currentUser.usedSickLeaveDays);
```

#### **步驟2：測試餘額計算**
```javascript
// 重新計算餘額
const newSickBalance = calculateActualSickLeaveBalance(currentUser.id);
console.log('重新計算的事病假餘額:', newSickBalance);

// 修正餘額
fixEmployeeSickLeaveBalance(currentUser.id);
```

#### **步驟3：測試編輯對話框**
1. 進入「排班申請」
2. 選擇月份
3. 點擊任一天進入編輯對話框
4. 檢查事病假餘額顯示
5. 觀察 Console 輸出：
   ```
   🔧 重新計算實際事病假餘額: X 天
   🎯 更新編輯對話框事病假餘額顯示:
      - 當前事病假天數: X
      - 實際餘額: X
      - 最終可用餘額: X
   ```

#### **步驟4：測試餘額檢查**
1. 選擇「事病假」選項
2. 觀察 Console 輸出：
   ```
   🎯 檢查事病假餘額: 實際餘額=X, 可用餘額=X
   ```

#### **步驟5：測試申請審核**
1. 提交包含事病假的申請
2. 使用主管帳號登入：<EMAIL> / super123
3. 批准申請
4. 觀察 Console 輸出：
   ```
   🏥 批准申請 - 扣減員工 XXX 事病假額度: 已用天數 X → X
   🔄 同步當前用戶事病假餘額: X
   ```

## 🔍 預期結果

### **正常情況下應該看到**
- **事病假餘額**：顯示實際可用天數（例如：18 天）
- **餘額計算**：基於年度額度減去已批准的使用天數
- **同步更新**：審核後餘額立即同步

### **Console 輸出範例**
```
🔧 重新計算實際事病假餘額: 18 天
🎯 更新編輯對話框事病假餘額顯示:
   - 當前事病假天數: 0
   - 實際餘額: 18
   - 最終可用餘額: 18
🎯 檢查事病假餘額: 實際餘額=18, 可用餘額=17
🏥 批准申請 - 扣減員工 員工 事病假額度: 已用天數 12 → 13
🔄 同步當前用戶事病假餘額: 17
```

## 🛠️ 除錯命令

### **檢查事病假狀態**
```javascript
// 檢查當前狀態
console.log('=== 事病假狀態檢查 ===');
console.log('actualSickLeaveBalance:', actualSickLeaveBalance);
console.log('currentUser.annualSickLeaveQuota:', currentUser.annualSickLeaveQuota);
console.log('currentUser.usedSickLeaveDays:', currentUser.usedSickLeaveDays);

// 重新計算餘額
console.log('=== 重新計算事病假餘額 ===');
const newBalance = calculateActualSickLeaveBalance(currentUser.id);
console.log('計算結果:', newBalance);

// 修正餘額
console.log('=== 修正事病假餘額 ===');
fixEmployeeSickLeaveBalance(currentUser.id);

// 重設所有員工餘額
console.log('=== 重設所有事病假餘額 ===');
resetAllSickLeaveBalances();
```

## ✅ 驗證清單

- [ ] 登入員工帳號成功
- [ ] Console 顯示事病假餘額初始化資訊
- [ ] 進入編輯對話框顯示正確的事病假餘額
- [ ] 選擇事病假時正確檢查餘額
- [ ] 提交申請後餘額正確扣除
- [ ] 審核批准後餘額同步更新
- [ ] 重新編輯時餘額計算正確
- [ ] 除錯工具正常運作

## 🎯 核心改進

1. **統一餘額來源**：所有事病假餘額顯示都來自 `actualSickLeaveBalance`
2. **實時計算**：基於已批准申請重新計算實際可用餘額
3. **自動同步**：審核後自動同步所有相關變數
4. **詳細追蹤**：完整的 Console 輸出可追蹤每個操作
5. **除錯工具**：提供工具自動修正餘額不一致問題

---

**修正版本**：v4.0
**測試日期**：2024年12月
**預期結果**：事病假餘額管理與額外班餘額一樣穩定可靠
