# 新對話銜接指南

## 🎯 當前狀態
❌ **事病假問題仍未解決**：需要參考額外班成功模式進行修正

## 📋 專案現狀

### **專案基本資訊**
- **專案名稱**：工作排班系統
- **主要檔案**：`schedule-app-simple.html`
- **開發者**：wenbin
- **技術棧**：HTML + JavaScript + Tailwind CSS
- **當前版本**：v9.0

### **本次對話發現的問題** ❌
- ❌ **事病假邏輯仍有錯誤**：各月份餘額顯示不一致
- ✅ **額外班邏輯正確**：可作為修正事病假的成功範本
- ✅ **登入功能穩定**：基本功能正常運作
- ❌ **統一餘額管理未完成**：事病假需要參考額外班模式

### **已完成功能** ✅
1. **用戶登入系統**：支援管理員、主管、員工三種角色
2. **排班申請功能**：員工可申請下月排班，支援多種班別
3. **審核系統**：主管可審核員工的排班申請
4. **額外班管理**：主管可新增額外班，員工可申請補休 ✅**邏輯正確**
5. **事病假管理**：年度額度管理 ❌**餘額顯示有問題**
6. **基本資料管理**：用戶資料、排班記錄的儲存與讀取

### **待解決問題** ❌
1. **事病假餘額統一**：各月份顯示不一致的實際餘額
2. **重新編輯邏輯**：錯誤扣除當月已批准天數
3. **函數修正需求**：`updateSickLeaveBalanceDisplay()` 等函數
4. **預覽機制缺失**：沒有正確的變化預覽

### **系統架構**
- **三種用戶角色**：管理員、主管、員工
- **核心功能**：排班申請、審核系統、額外班管理、事病假管理
- **資料結構**：本地 localStorage 儲存
- **介面設計**：響應式設計，月曆式操作

## 🚨 當前緊急問題

### **主要問題：額外班餘額計算錯誤**
- **現象**：主管批准包含補休的申請後，員工額外班餘額不正確
- **影響**：員工無法正確使用補休功能，系統資料不一致
- **優先級**：🔴 **最高優先級**

### **問題細節**
1. 重新編輯已批准申請時，餘額顯示錯誤
2. 可能存在重複扣除或恢復的問題
3. 不同頁面顯示的餘額可能不一致

## 📁 重要文檔

### **已準備的文檔**
1. `工作排班系統完整開發文檔.md` - 完整系統架構和開發狀態
2. `系統功能需求清單.md` - 詳細功能需求和完成狀態
3. `當前問題與解決方案.md` - 問題分析和解決方案
4. `工作排班系統開發備忘錄.md` - 原始需求規格
5. `專案開發記錄_額外班餘額問題.md` - 問題追蹤記錄

### **關鍵資訊位置**
- **系統架構**：工作排班系統完整開發文檔.md 第20-80行
- **資料結構**：工作排班系統完整開發文檔.md 第82-140行
- **問題分析**：當前問題與解決方案.md 第5-50行
- **功能狀態**：系統功能需求清單.md 全文

## 🔧 關鍵程式碼位置

### **需要檢查的函數**
```javascript
// 主管審核處理 - 第2719-2770行
processApproval(requestId, status, comment)

// 員工提交申請 - 第2150-2158行
submitScheduleRequest()

// 餘額重新計算 - 第3972-4001行
recalculateEmployeeOvertimeBalance(employeeId)

// 餘額顯示更新 - 第1962-2000行
updateOvertimeBalanceInSummary()
```

### **資料結構重點**
```javascript
// 申請記錄結構
scheduleRequest = {
    originalCompensatoryDays: 0, // 重新編輯時的原始值
    compensatoryDaysCount: 2,    // 當前補休天數
    hasCompensatoryLeave: true   // 是否包含補休
}

// 用戶資料結構
user = {
    overtimeBalance: 5  // 額外班餘額
}
```

## 🧪 測試場景

### **基本測試流程**
1. **設定員工額外班**：10天
2. **員工申請補休**：3天
3. **主管批准申請**：檢查餘額是否變成7天
4. **員工重新編輯**：
   - 增加補休到5天 → 餘額應該變成5天
   - 減少補休到1天 → 餘額應該變成9天
   - 完全取消補休 → 餘額應該變成10天

### **除錯指令**
```javascript
// 檢查系統狀態
debugSystemData();

// 檢查特定員工的額外班記錄
systemData.overtimeRecords.filter(r => r.employeeId === '3');

// 檢查員工餘額
systemData.users.find(u => u.id === '3').overtimeBalance;

// 手動重新計算
recalculateEmployeeOvertimeBalance('3');
```

## 🎯 新對話行動計畫

### **第一步：問題診斷**
1. 檢查當前額外班餘額計算邏輯
2. 追蹤餘額變化的完整流程
3. 識別重複處理的位置

### **第二步：問題修正**
1. 修正主管審核時的餘額處理邏輯
2. 確保重新編輯時的差額計算正確
3. 統一所有餘額顯示邏輯

### **第三步：測試驗證**
1. 測試所有補休場景
2. 驗證資料一致性
3. 確認問題完全解決

### **第四步：功能完善**
1. 實現用戶管理系統
2. 添加統計報表功能
3. 完善系統設定介面

## 💡 開發建議

### **立即行動**
- 專注於修正額外班餘額問題
- 使用詳細的 Console 輸出追蹤問題
- 確保每個修正都經過完整測試

### **開發原則**
- 保持程式碼簡潔清晰
- 確保資料一致性
- 提供良好的用戶體驗
- 實施充分的錯誤處理

### **測試策略**
- 每次修改後立即測試
- 覆蓋所有使用場景
- 驗證邊界條件
- 確保向後相容性

## 📞 溝通要點

### **與 AI 助手溝通時**
1. **明確問題描述**：具體說明遇到的問題
2. **提供錯誤資訊**：包含 Console 輸出和錯誤訊息
3. **說明測試場景**：描述具體的操作步驟
4. **確認修正效果**：驗證問題是否完全解決

### **開發進度追蹤**
- 記錄每次修正的內容
- 更新問題狀態
- 維護測試結果
- 規劃下一步行動

## 🚀 成功標準

### **問題修正成功標準**
- ✅ 額外班餘額計算完全正確
- ✅ 重新編輯功能正常運作
- ✅ 所有頁面餘額顯示一致
- ✅ 通過所有測試場景

### **系統完成標準**
- ✅ 所有核心功能正常運作
- ✅ 用戶體驗良好
- ✅ 程式碼品質優良
- ✅ 系統穩定可靠

## 🔧 關鍵成功方法

### **1. 餘額管理的正確模式**
```javascript
// ✅ 正確：直接使用用戶資料
const totalBalance = currentUser.overtimeBalance || 0;
const sickBalance = (currentUser.annualSickLeaveQuota || 0) - (currentUser.usedSickLeaveDays || 0);

// ❌ 錯誤：扣除當前選擇的天數
const wrongBalance = totalBalance - currentSelectedDays;
```

### **2. 預覽機制實現**
```javascript
// 根據原始天數計算差額
if (window.originalCompensatoryDays !== undefined) {
    const difference = currentDays - originalDays;
    if (difference > 0) {
        balanceDisplay.innerHTML = `將扣除 ${difference} 天，剩餘 ${totalBalance - difference} 天`;
    }
}
```

### **3. 重新編輯處理**
```javascript
// 記錄原始天數
window.originalCompensatoryDays = existingRequest.compensatoryDaysCount || 0;
window.originalSickLeaveDays = existingRequest.sickLeaveDaysCount || 0;

// 不在編輯時恢復餘額，只在審核批准後調整
```

## 🎯 下個對話開始建議

### **開場說明**
"我是 wenbin，正在開發工作排班系統。目前基本功能已完成，但事病假餘額管理有問題：各月份顯示的餘額不一致。額外班餘額的邏輯是正確的，需要參考額外班的成功模式來修正事病假。主要檔案是 schedule-app-simple.html。請參考 '工作排班系統開發完整記錄.md' 了解問題詳情和額外班的成功解決方案。"

### **立即任務**
1. **分析事病假問題**：理解為什麼各月份餘額顯示不一致
2. **研究額外班成功模式**：了解額外班是如何正確處理的
3. **修正事病假邏輯**：完全仿照額外班的成功做法
4. **測試驗證修正**：確保各月份顯示一致的實際餘額

### **關鍵問題描述**
員工A餘額2天，7月已批准1天事病假，8月已批准2天事病假：
- **錯誤**：進入7月編輯顯示1天，進入8月編輯顯示0天
- **正確**：進入任何月份編輯都應該顯示2天

---

**指南版本**：v3.0 - 事病假問題待修正版
**更新時間**：2024年12月
**適用對話**：新對話問題修正銜接
**系統狀態**：❌ 事病假邏輯需要修正，參考額外班成功模式
