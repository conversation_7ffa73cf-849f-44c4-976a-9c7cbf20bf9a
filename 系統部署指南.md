# 工作排班管理系統部署指南

## 🎯 部署概述

**工作排班管理系統**採用純前端技術開發，無需後端服務器或資料庫，可以快速部署在任何支援現代瀏覽器的環境中。

### **部署特點**

- 🚀 **零配置部署**：無需安裝額外軟體
- 💾 **本地儲存**：資料儲存在瀏覽器 LocalStorage
- 🌐 **跨平台**：支援 Windows、macOS、Linux
- 📱 **響應式**：支援桌面、平板、手機
- 🔒 **資料安全**：資料不會上傳到外部服務器

## 📋 系統需求

### **硬體需求**

- **處理器**：任何現代處理器
- **記憶體**：至少 2GB RAM
- **儲存空間**：至少 10MB 可用空間
- **螢幕解析度**：建議 1024x768 以上

### **軟體需求**

- **作業系統**：Windows 7+、macOS 10.10+、Linux（任何發行版）
- **瀏覽器**：
  - Chrome 60+
  - Firefox 55+
  - Safari 11+
  - Edge 79+
  - Opera 47+

### **網路需求**

- **初次載入**：需要網路連線載入 CDN 資源
- **日常使用**：完全離線運作
- **通知功能**：需要網路連線（Email/LINE 通知）

## 🚀 快速部署

### **方法一：直接使用**

1. 下載 `schedule-app-simple.html` 檔案
2. 雙擊檔案，系統會在預設瀏覽器中開啟
3. 使用測試帳號登入或註冊新帳號
4. 開始使用系統

### **方法二：Web 服務器部署**

1. 將 `schedule-app-simple.html` 放置在 Web 服務器目錄
2. 透過 HTTP/HTTPS 訪問檔案
3. 支援多用戶同時訪問（各自獨立的資料）

### **方法三：內網部署**

1. 在內網服務器上放置檔案
2. 員工透過內網 IP 訪問
3. 適合企業內部使用

## 🏢 企業部署方案

### **小型企業（10-50人）**

#### **部署方式**

- 直接檔案分發
- 每個用戶在自己的電腦上運行
- 定期收集資料進行統一管理

#### **優點**

- 部署簡單，成本低
- 資料分散，風險低
- 無需專業 IT 維護

#### **缺點**

- 資料不統一
- 需要手動同步
- 協作功能受限

### **中型企業（50-200人）**

#### **部署方式**

- 內網 Web 服務器部署
- 統一訪問入口
- 集中式資料管理

#### **架構建議**

```
內網服務器 (IIS/Apache/Nginx)
├── schedule-app-simple.html
├── 備份目錄
└── 日誌目錄
```

#### **優點**

- 統一管理
- 資料集中
- 易於維護

#### **缺點**

- 需要服務器
- 需要 IT 維護
- 單點故障風險

### **大型企業（200+人）**

#### **部署方式**

- 多節點負載均衡
- 資料同步機制
- 災難恢復方案

#### **架構建議**

```
負載均衡器
├── Web 服務器 1
├── Web 服務器 2
├── Web 服務器 3
└── 備份服務器
```

## 🔧 詳細部署步驟

### **Windows 環境部署**

#### **IIS 部署**

1. 啟用 IIS 功能

   ```
   控制台 → 程式和功能 → 開啟或關閉 Windows 功能 → Internet Information Services
   ```
2. 建立網站

   ```
   IIS 管理員 → 新增網站
   網站名稱：WorkSchedule
   實體路徑：C:\inetpub\wwwroot\schedule
   連接埠：80 或 8080
   ```
3. 複製檔案

   ```
   將 schedule-app-simple.html 複製到 C:\inetpub\wwwroot\schedule\
   ```
4. 設定權限

   ```
   確保 IIS_IUSRS 有讀取權限
   ```
5. 測試訪問

   ```
   瀏覽器開啟：http://localhost/schedule-app-simple.html
   ```

#### **Apache 部署**

1. 安裝 Apache

   ```
   下載 Apache for Windows
   解壓到 C:\Apache24
   ```
2. 配置 httpd.conf

   ```apache
   DocumentRoot "C:/Apache24/htdocs"
   <Directory "C:/Apache24/htdocs">
       Options Indexes FollowSymLinks
       AllowOverride None
       Require all granted
   </Directory>
   ```
3. 複製檔案

   ```
   將 schedule-app-simple.html 複製到 C:\Apache24\htdocs\
   ```
4. 啟動服務

   ```cmd
   C:\Apache24\bin\httpd.exe
   ```

### **Linux 環境部署**

#### **Nginx 部署**

1. 安裝 Nginx

   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install nginx

   # CentOS/RHEL
   sudo yum install nginx
   ```
2. 配置網站

   ```nginx
   # /etc/nginx/sites-available/schedule
   server {
       listen 80;
       server_name your-domain.com;
       root /var/www/schedule;
       index schedule-app-simple.html;

       location / {
           try_files $uri $uri/ =404;
       }
   }
   ```
3. 啟用網站

   ```bash
   sudo ln -s /etc/nginx/sites-available/schedule /etc/nginx/sites-enabled/
   sudo mkdir -p /var/www/schedule
   sudo cp schedule-app-simple.html /var/www/schedule/
   sudo systemctl restart nginx
   ```

#### **Apache 部署**

1. 安裝 Apache

   ```bash
   # Ubuntu/Debian
   sudo apt install apache2

   # CentOS/RHEL
   sudo yum install httpd
   ```
2. 配置虛擬主機

   ```apache
   # /etc/apache2/sites-available/schedule.conf
   <VirtualHost *:80>
       ServerName your-domain.com
       DocumentRoot /var/www/schedule
       <Directory /var/www/schedule>
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```
3. 啟用網站

   ```bash
   sudo a2ensite schedule
   sudo mkdir -p /var/www/schedule
   sudo cp schedule-app-simple.html /var/www/schedule/
   sudo systemctl restart apache2
   ```

### **macOS 環境部署**

#### **使用內建 Apache**

1. 啟用 Apache

   ```bash
   sudo apachectl start
   ```
2. 複製檔案

   ```bash
   sudo cp schedule-app-simple.html /usr/local/var/www/
   ```
3. 訪問系統

   ```
   http://localhost/schedule-app-simple.html
   ```

## 🔒 安全配置

### **HTTPS 配置**

#### **自簽名證書**

```bash
# 生成私鑰
openssl genrsa -out schedule.key 2048

# 生成證書請求
openssl req -new -key schedule.key -out schedule.csr

# 生成自簽名證書
openssl x509 -req -days 365 -in schedule.csr -signkey schedule.key -out schedule.crt
```

#### **Nginx HTTPS 配置**

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
  
    ssl_certificate /path/to/schedule.crt;
    ssl_certificate_key /path/to/schedule.key;
  
    root /var/www/schedule;
    index schedule-app-simple.html;
}
```

### **訪問控制**

#### **IP 白名單**

```nginx
# Nginx
location / {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
}
```

```apache
# Apache
<Directory "/var/www/schedule">
    Require ip 192.168.1
    Require ip 10.0.0
</Directory>
```

#### **基本認證**

```nginx
# Nginx
location / {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
}
```

```apache
# Apache
<Directory "/var/www/schedule">
    AuthType Basic
    AuthName "Restricted Area"
    AuthUserFile /etc/apache2/.htpasswd
    Require valid-user
</Directory>
```

## 📊 監控與維護

### **日誌監控**

#### **Nginx 日誌**

```bash
# 訪問日誌
tail -f /var/log/nginx/access.log

# 錯誤日誌
tail -f /var/log/nginx/error.log
```

#### **Apache 日誌**

```bash
# 訪問日誌
tail -f /var/log/apache2/access.log

# 錯誤日誌
tail -f /var/log/apache2/error.log
```

### **效能監控**

#### **系統資源監控**

```bash
# CPU 和記憶體使用率
top
htop

# 磁碟使用率
df -h

# 網路連線
netstat -an
```

#### **Web 服務器監控**

```bash
# Nginx 狀態
sudo systemctl status nginx

# Apache 狀態
sudo systemctl status apache2
```

### **備份策略**

#### **檔案備份**

```bash
# 建立備份目錄
mkdir -p /backup/schedule

# 定期備份
cp /var/www/schedule/schedule-app-simple.html /backup/schedule/schedule-$(date +%Y%m%d).html
```

#### **自動備份腳本**

```bash
#!/bin/bash
# backup-schedule.sh

BACKUP_DIR="/backup/schedule"
SOURCE_FILE="/var/www/schedule/schedule-app-simple.html"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
cp $SOURCE_FILE $BACKUP_DIR/schedule-$DATE.html

# 保留最近 30 天的備份
find $BACKUP_DIR -name "schedule-*.html" -mtime +30 -delete
```

#### **定時任務**

```bash
# 編輯 crontab
crontab -e

# 每天凌晨 2 點備份
0 2 * * * /path/to/backup-schedule.sh
```

## 🔧 故障排除

### **常見問題**

#### **檔案無法訪問**

- 檢查檔案路徑是否正確
- 確認檔案權限設定
- 檢查 Web 服務器配置

#### **樣式顯示異常**

- 檢查 CDN 資源是否可訪問
- 確認網路連線正常
- 嘗試重新整理頁面

#### **功能無法使用**

- 檢查瀏覽器版本是否支援
- 確認 JavaScript 是否啟用
- 檢查瀏覽器控制台錯誤

### **效能優化**

#### **快取設定**

```nginx
# Nginx 快取配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

```apache
# Apache 快取配置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
</IfModule>
```

#### **壓縮設定**

```nginx
# Nginx 壓縮
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

```apache
# Apache 壓縮
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

## 📞 技術支援

### **部署支援**

- 檢查系統需求是否滿足
- 確認部署步驟是否正確
- 查看錯誤日誌獲取詳細資訊

### **維護支援**

- 定期檢查系統運行狀態
- 及時更新安全補丁
- 監控系統效能指標

### **聯繫方式**

- **技術文件**：參考本部署指南
- **系統管理員**：<EMAIL>
- **緊急支援**：請聯繫 IT 部門

---

**工作排班管理系統部署指南 v1.0**
© 2024 - 企業級部署解決方案








## 🚀 系統部署指導

### 📋 **部署選項概覽**

根據您的需求和技術能力，我推薦以下幾種部署方式：

#### **選項A：本地桌面部署（推薦新手）**

* ✅ 最簡單，無需伺服器
* ✅ 適合小團隊使用
* ✅ 資料存在本地，安全性高
* ❌ 只能單機使用

#### **選項B：區域網路部署（推薦中小企業）**

* ✅ 團隊內部共用
* ✅ 設定相對簡單
* ✅ 成本低廉
* ❌ 需要內網環境

#### **選項C：雲端部署（推薦大型組織）**

* ✅ 隨時隨地存取
* ✅ 高可用性
* ✅ 專業維護
* ❌ 需要技術知識和費用

---

## 🖥️ **選項A：本地桌面部署**

### **步驟1：準備檔案**

1. 將 `schedule-app-simple.html`
   儲存到您的電腦
2. 建議放在專用資料夾，如：`C:\WorkScheduleSystem\`

### **步驟2：設定瀏覽器**

1. 使用 **Chrome** 或 **Edge** 瀏覽器（推薦）
2. 直接雙擊 HTML 檔案開啟
3. 建立書籤方便日後使用

### **步驟3：初始設定**

1. 首次開啟會自動建立管理員帳號
2. 設定系統基本參數
3. 建立部門和用戶

### **優點** ：

* 立即可用，無需額外設定
* 資料存在瀏覽器本地儲存
* 完全免費

### **限制** ：

* 只能在同一台電腦使用
* 清除瀏覽器資料會遺失系統資料

---

## 🏢 **選項B：區域網路部署**

### **方法B1：使用 Python 簡易伺服器**

#### **步驟1：安裝 Python**

**Copy**

**# 下載並安裝 Python（https://python.org）**

**# 確認安裝成功**

**python **--version

#### **步驟2：建立專案資料夾**

**Copy**

**mkdir WorkScheduleSystem**

**cd WorkScheduleSystem**

**# 將 schedule**-**app**-**simple**.**html 放入此資料夾**

#### **步驟3：啟動伺服器**

**Copy**

**# Python 3**

**python -m http.server 8080**

**# 或 Python 2**

**python -m SimpleHTTPServer 8080**

#### **步驟4：存取系統**

* 本機存取：`http://localhost:8080/schedule-app-simple.html`
* 區網存取：`http://[您的IP]:8080/schedule-app-simple.html`

### **方法B2：使用 Node.js**

#### **步驟1：安裝 Node.js**

**Copy**

**# 下載並安裝 Node.js（https://nodejs.org）**

**npm install **-**g http**-**server**

#### **步驟2：啟動伺服器**

**Copy**

**cd** **WorkScheduleSystem**

**http**-**server** -p** **8080

### **方法B3：使用 XAMPP（推薦）**

#### **步驟1：下載安裝 XAMPP**

* 前往 [https://www.apachefriends.org/](https://www.apachefriends.org/)
* 下載並安裝 XAMPP

#### **步驟2：設定檔案**

**Copy**

**# 將 schedule-app-simple.html 複製到**

**C**:**\xampp\htdocs\schedule\**

#### **步驟3：啟動服務**

1. 開啟 XAMPP Control Panel
2. 啟動 Apache
3. 存取：`http://localhost/schedule/schedule-app-simple.html`

---

## ☁️ **選項C：雲端部署**

### **方法C1：GitHub Pages（免費）**

#### **步驟1：建立 GitHub 帳號**

* 前往 [https://github.com](https://github.com/) 註冊

#### **步驟2：建立儲存庫**

**Copy**

**# 建立新的 repository**

**# 名稱：work-schedule-system**

**# 設為 Public**

#### **步驟3：上傳檔案**

1. 將 `schedule-app-simple.html`
   重新命名為 `index.html`
2. 上傳到 GitHub repository

#### **步驟4：啟用 GitHub Pages**

1. 進入 repository 設定
2. 找到 "Pages" 選項
3. 選擇 "Deploy from a branch"
4. 選擇 "main" branch
5. 儲存設定

#### **步驟5：存取系統**

* 網址：`https://[您的用戶名].github.io/work-schedule-system/`

### **方法C2：Netlify（免費）**

#### **步驟1：準備檔案**

1. 將 `schedule-app-simple.html`
   重新命名為 `index.html`
2. 壓縮成 ZIP 檔案

#### **步驟2：部署到 Netlify**

1. 前往 [https://netlify.com](https://netlify.com/)
2. 註冊帳號
3. 拖拉 ZIP 檔案到部署區域
4. 等待部署完成

#### **步驟3：自訂網域（選填）**

* 可以設定自訂網域名稱
* 自動提供 HTTPS

### **方法C3：Vercel（免費）**

#### **步驟1：安裝 Vercel CLI**

**Copy**

**npm install **-**g vercel**

#### **步驟2：部署**

**Copy**

**cd WorkScheduleSystem**

**vercel **--**prod**

---

## 🔧 **進階部署設定**

### **資料庫整合（選填）**

如果您需要更強大的資料管理，可以考慮整合資料庫：

#### **選項1：Firebase（推薦）**

**Copy**

**// 在 HTML 中加入 Firebase SDK**

**<**script src**=**"https://www.gstatic.com/firebasejs/9.

**0.0/firebase-app.js"**></script**>**

**<**script src**=**"https://www.gstatic.com/firebasejs/9.

**0.0/firebase-firestore.js"**></script**>**

**// 設定 Firebase 配置**

**const** firebaseConfig **=** **{**

**  **// 您的 Firebase 配置

**}**;

#### **選項2：Supabase**

**Copy**

**// 整合 Supabase 作為後端資料庫**

**<**script src**=**"https://cdn.jsdelivr.net/npm/

**@supabase/supabase-js@2"**></script**>**

### **HTTPS 設定**

#### **免費 SSL 憑證**

* **Let's Encrypt** ：免費 SSL 憑證
* **Cloudflare** ：免費 CDN 和 SSL

#### **設定步驟**

1. 註冊 Cloudflare 帳號
2. 新增您的網域
3. 更新 DNS 設定
4. 啟用 SSL/TLS

---

## 📱 **行動裝置優化**

### **PWA 設定（漸進式網頁應用）**

#### **步驟1：建立 manifest.json**

**Copy**

**{**

**  **"name"**:** **"工作排班管理系統"**,

**  **"short_name"**:** **"排班系統"**,

**  **"description"**:** **"完整的工作排班管理解決方案"**,

**  **"start_url"**:** **"/"**,

**  **"display"**:** **"standalone"**,

**  **"background_color"**:** **"#ffffff"**,

**  **"theme_color"**:** **"#4f46e5"**,

**  **"icons"**:** **[**

**    **{

**      **"src"**:** **"icon-192.png"**,

**      **"sizes"**:** **"192x192"**,

**      **"type"**:** **"image/png"**

**    **}

**  **]

**}**

#### **步驟2：在 HTML 中引用**

**Copy**

**<**link** **rel**=**"manifest"** **href**=**"manifest.json"**>**

**<**meta** **name**=**"theme-color"** **content**=**"#4f46e5"**>**

---

## 🔒 **安全性設定**

### **基本安全措施**

#### **1. 存取控制**

**Copy**

**// 在系統中設定 IP 白名單（如需要）**

**const** allowedIPs **=** **[**'***********/24'**]**;

#### **2. 資料備份**

**Copy**

**// 定期匯出系統資料**

**function** exportSystemData**(**)** **{

**  **const** data **=** **JSON**.**stringify**(**systemData**)**;

**  **const** blob **=** **new** **Blob**(**[**data**]**,** **{**type**:**

  **'application/json'**}**)**;

**  **const** **url** **=** **URL**.**createObjectURL**(**blob**)**;

**  **// 下載備份檔案

**}**

#### **3. 密碼政策**

* 建議設定強密碼
* 定期更換密碼
* 啟用雙因素驗證（如整合 LINE 登入）

---

## 📊 **效能優化**

### **快取設定**

**Copy**

**`<!-- 在 HTML head 中加入 -->`**

**<**meta** **http-equiv**=**"Cache-Control"** **

**content**=**"max-age=31536000"**>

### **壓縮設定**

**Copy**

**# 使用 gzip 壓縮（Apache）**

**# 在 **.**htaccess 中加入**

**<**IfModule mod_deflate**.**c**>**

**    AddOutputFilterByType DEFLATE **text**/**html **text**/

    **css**text**/**javascript

**</**IfModule**>**

---

## 🎯 **部署建議**

### **根據組織規模選擇**

#### **小型團隊（5-10人）**

* **推薦** ：本地部署 + XAMPP
* **備案** ：GitHub Pages

#### **中型企業（10-50人）**

* **推薦** ：區域網路部署 + XAMPP
* **備案** ：Netlify + 自訂網域

#### **大型組織（50人以上）**

* **推薦** ：雲端部署 + Firebase
* **備案** ：專業主機 + 資料庫

### **技術能力考量**

#### **非技術人員**

1. 本地桌面部署
2. GitHub Pages
3. Netlify 拖拉部署

#### **有基礎技術能力**

1. XAMPP 區網部署
2. Node.js 伺服器
3. Vercel 部署

#### **專業技術團隊**

1. Docker 容器化部署
2. AWS/Azure 雲端部署
3. 自建伺服器 + 資料庫

---

## 🆘 **常見問題解決**

### **問題1：無法存取系統**

* 檢查防火牆設定
* 確認 IP 位址正確
* 檢查伺服器是否啟動

### **問題2：資料遺失**

* 定期備份系統資料
* 使用雲端儲存
* 設定自動備份

### **問題3：效能問題**

* 清除瀏覽器快取
* 檢查網路連線
* 優化系統設定

---

## 📞 **後續支援**

### **維護建議**

1. **定期備份** ：每週備份系統資料
2. **更新瀏覽器** ：保持瀏覽器最新版本
3. **監控使用** ：定期檢查系統使用狀況
4. **用戶培訓** ：提供用戶操作培訓

### **擴展功能**

如果未來需要新增功能，可以考慮：

* 整合打卡系統
* 薪資計算模組
* 報表分析功能
* 行動 APP 開發

---

## 🎉 **部署完成檢查清單**

* 系統可以正常開啟
* 管理員帳號設定完成
* 基本系統參數設定
* 部門架構建立
* 用戶帳號建立
* 通知功能測試
* 排班功能測試
* 資料備份設定
* 用戶培訓完成
