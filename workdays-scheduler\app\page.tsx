import Link from "next/link";
import { Calendar, Users, Clock, BarChart3 } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-indigo-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">排班管理系統</h1>
            </div>
            <nav className="flex space-x-4">
              <Link href="/login" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                登入
              </Link>
              <Link href="/register" className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                註冊
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            智慧排班管理解決方案
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            簡化排班流程，提升工作效率。支援員工排班申請、主管審批、調班管理等完整功能。
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="/login" className="bg-indigo-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-indigo-700 transition-colors">
              立即開始
            </Link>
            <Link href="#features" className="border border-indigo-600 text-indigo-600 px-8 py-3 rounded-lg text-lg font-medium hover:bg-indigo-50 transition-colors">
              了解更多
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <Users className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">多角色管理</h3>
            <p className="text-gray-600">支援管理員、主管、員工三種角色，權限分明</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <Calendar className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">智慧排班</h3>
            <p className="text-gray-600">支援多種班次類型，自動檢查人力需求</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <Clock className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">調班申請</h3>
            <p className="text-gray-600">便捷的調班申請流程，主管線上審批</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <BarChart3 className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">數據報表</h3>
            <p className="text-gray-600">完整的工時統計與排班報表功能</p>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">系統功能特色</h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">排班管理</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 員工每月提交下月排班需求</li>
                <li>• 支援早班、中班、彈性班等多種班次</li>
                <li>• 自動檢查慈光、瑞光兩地最低人力需求</li>
                <li>• 主管線上審批排班申請</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">調班與通知</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 前一天截止的調班申請機制</li>
                <li>• 郵件與LINE通知功能</li>
                <li>• 月度與年度工時統計報表</li>
                <li>• A4格式排班公告表</li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 排班管理系統. 版權所有.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
