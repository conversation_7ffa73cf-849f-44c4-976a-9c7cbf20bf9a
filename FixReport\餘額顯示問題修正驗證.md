# 餘額顯示問題修正驗證

## 🎯 問題描述
進入某月份排班表的某日要進行選擇工作班別或請假時，補休及事病假及請假這三個選項的可用額度皆是0。

## 🔧 修正內容

### **1. 修正編輯對話框餘額顯示**
- 修正 `updateOvertimeBalanceDisplay()` 函數使用 `actualOvertimeBalance`
- 修正 `saveDayShift()` 函數的餘額檢查邏輯
- 在 `generateCalendar()` 函數中確保 `actualOvertimeBalance` 正確設定

### **2. 添加詳細除錯輸出**
- 在 `initCustomShiftSettings()` 中添加餘額狀態輸出
- 追蹤餘額計算過程

## 🧪 測試步驟

### **準備工作**
1. 開啟瀏覽器開發者工具（F12）
2. 切換到 Console 標籤
3. 登入員工帳號：<EMAIL> / emp123

### **測試流程**

#### **步驟1：檢查初始狀態**
```javascript
// 在 Console 中執行
console.log('當前用戶:', currentUser);
console.log('實際餘額:', actualOvertimeBalance);
console.log('事病假額度:', currentUser.annualSickLeaveQuota);
console.log('已用事病假:', currentUser.usedSickLeaveDays);
```

#### **步驟2：進入排班申請**
1. 點擊「排班申請」
2. 選擇一個月份（例如：2024-12）
3. 觀察 Console 輸出，應該看到：
   ```
   🔧 初始化編輯對話框 - actualOvertimeBalance: X
   🔧 初始化編輯對話框 - currentUser.overtimeBalance: X
   🔧 初始化編輯對話框 - currentUser.annualSickLeaveQuota: 30
   🔧 初始化編輯對話框 - currentUser.usedSickLeaveDays: X
   ```

#### **步驟3：點擊某一天編輯**
1. 點擊月曆中的任一天
2. 檢查編輯對話框中的餘額顯示：
   - **補休**：應該顯示實際可用餘額（不是0）
   - **事病假**：應該顯示剩餘額度（30 - 已使用天數）
   - **請假**：正常顯示

#### **步驟4：測試餘額計算**
1. 選擇「補休」選項
2. 觀察 Console 輸出：
   ```
   🎯 更新編輯對話框額外班餘額顯示:
      - 當前補休天數: X
      - 實際餘額: X
      - 原始補休天數: X
   ```

## 🔍 預期結果

### **正常情況下應該看到**
- **補休餘額**：顯示實際可用天數（例如：1.5 天）
- **事病假餘額**：顯示剩餘額度（例如：18 天）
- **請假選項**：正常可選

### **如果仍然顯示0**
請在 Console 中執行以下除錯命令：

```javascript
// 檢查系統狀態
debugSystemData();

// 手動重新計算餘額
actualOvertimeBalance = calculateActualOvertimeBalance(currentUser.id);
console.log('重新計算後的餘額:', actualOvertimeBalance);

// 修正餘額
fixEmployeeOvertimeBalance(currentUser.id);

// 重新進入編輯對話框測試
```

## 🛠️ 常見問題排除

### **問題1：補休餘額仍然是0**
**原因**：可能沒有額外班記錄
**解決**：
1. 使用主管帳號登入：<EMAIL> / super123
2. 進入「額外班管理」
3. 為員工添加額外班記錄

### **問題2：事病假餘額是0**
**原因**：可能年度額度設定為0或已用完
**解決**：
1. 檢查 `currentUser.annualSickLeaveQuota`
2. 檢查 `currentUser.usedSickLeaveDays`
3. 如需調整，使用管理員帳號修改

### **問題3：Console 沒有除錯輸出**
**原因**：可能瀏覽器快取問題
**解決**：
1. 強制重新整理頁面（Ctrl+F5）
2. 清除瀏覽器快取
3. 重新登入

## 📊 除錯命令參考

```javascript
// 檢查當前狀態
console.log('=== 當前狀態檢查 ===');
console.log('currentUser:', currentUser);
console.log('actualOvertimeBalance:', actualOvertimeBalance);
console.log('systemData.overtimeRecords:', systemData.overtimeRecords);

// 重新計算餘額
console.log('=== 重新計算餘額 ===');
const newBalance = calculateActualOvertimeBalance(currentUser.id);
console.log('計算結果:', newBalance);

// 修正餘額
console.log('=== 修正餘額 ===');
fixEmployeeOvertimeBalance(currentUser.id);

// 檢查系統一致性
console.log('=== 系統一致性檢查 ===');
debugSystemData();
```

## ✅ 驗證清單

- [ ] 登入員工帳號成功
- [ ] Console 顯示初始化除錯資訊
- [ ] 進入排班申請頁面
- [ ] 選擇月份後顯示月曆
- [ ] 點擊某天進入編輯對話框
- [ ] 補休選項顯示正確餘額（不是0）
- [ ] 事病假選項顯示正確餘額（不是0）
- [ ] 請假選項正常可選
- [ ] Console 顯示詳細的餘額計算過程

---

**修正版本**：v3.0
**測試日期**：2024年12月
**預期結果**：所有餘額正確顯示，不再出現0的情況
