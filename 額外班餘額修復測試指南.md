# 額外班餘額修復測試指南

## 🎯 修復內容摘要

已完成額外班餘額計算錯誤的修復，主要解決了以下問題：
1. 重新編輯已批准申請時餘額顯示錯誤
2. 餘額計算邏輯不一致
3. 缺乏統一的實際餘額管理

## 🧪 測試場景

### **場景1：基本功能測試**

1. **登入員工帳戶**
   ```
   Email: <EMAIL>
   Password: emp123
   ```

2. **檢查初始狀態**
   - 在瀏覽器 Console 執行：`debugSystemData()`
   - 確認員工初始額外班餘額顯示正確

3. **新增額外班記錄**
   - 使用主管帳戶 (<EMAIL> / super123)
   - 進入「額外班管理」
   - 為員工新增 5 天額外班
   - 確認員工餘額更新為 5 天

### **場景2：新申請測試**

1. **員工提交包含補休的申請**
   - 選擇下個月份
   - 設定 3 天補休
   - 確認系統顯示「將扣除 3 天，剩餘 2 天」
   - 提交申請

2. **主管批准申請**
   - 使用主管帳戶登入
   - 進入「待審核申請」
   - 批准申請
   - 確認員工餘額變成 2 天

### **場景3：重新編輯測試（核心修復）**

1. **員工重新編輯已批准申請**
   - 選擇已批准的月份
   - 確認系統顯示實際餘額 2 天（不是錯誤的 5 天）
   - 將補休從 3 天改為 5 天
   - 確認系統顯示「將扣除 2 天，剩餘 0 天」

2. **主管批准重新編輯**
   - 批准修改後的申請
   - 確認員工餘額變成 0 天

3. **測試減少補休**
   - 員工再次編輯，將補休從 5 天改為 1 天
   - 確認系統顯示「將恢復 4 天，剩餘 4 天」
   - 主管批准後確認餘額變成 4 天

### **場景4：完全取消補休測試**

1. **員工取消所有補休**
   - 重新編輯申請，將所有補休改為休假
   - 確認系統顯示「將恢復 1 天，剩餘 5 天」
   - 主管批准後確認餘額恢復到 5 天

## 🔍 除錯工具使用

### **檢查系統狀態**
```javascript
debugSystemData();
```

### **修正特定員工餘額**
```javascript
fixEmployeeOvertimeBalance('3'); // 員工ID
```

### **重置所有餘額**
```javascript
resetAllOvertimeBalances();
```

### **手動計算實際餘額**
```javascript
calculateActualOvertimeBalance('3'); // 員工ID
```

## ✅ 預期結果

1. **餘額顯示一致**：所有頁面顯示相同的實際可用餘額
2. **重新編輯正確**：不會錯誤恢復已扣除的餘額
3. **差額計算準確**：正確計算補休天數變化的差額
4. **資料同步完整**：所有餘額變數保持同步

## ⚠️ 注意事項

1. **測試順序**：建議按照場景順序測試
2. **Console 監控**：全程監控 Console 輸出
3. **資料備份**：測試前可匯出資料備份
4. **清理資料**：測試完成後可重置系統

## 🎯 修復驗證

如果所有測試場景都通過，表示額外班餘額問題已完全解決：
- ✅ 重新編輯時不再錯誤恢復餘額
- ✅ 所有餘額顯示都正確
- ✅ 差額計算邏輯正確
- ✅ 資料同步機制完善

## 📞 問題回報

如果測試中發現任何問題，請提供：
1. 具體操作步驟
2. Console 錯誤訊息
3. 預期結果 vs 實際結果
4. 系統狀態（debugSystemData() 輸出）
