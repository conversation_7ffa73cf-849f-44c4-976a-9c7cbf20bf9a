# Google Sheets 範本設定

## 📊 建議的 Google Sheets 結構

### 工作表 1: 用戶資料 (Users)
| A | B | C | D | E | F |
|---|---|---|---|---|---|
| ID | 姓名 | 電子郵件 | 角色 | 地點 | 狀態 |
| 1 | 系統管理員 | <EMAIL> | admin | ciguang | approved |
| 2 | 主管 | <EMAIL> | supervisor | ciguang | approved |
| 3 | 員工 | <EMAIL> | employee | ruiguang | approved |
| 4 | 張小明 | <EMAIL> | employee | ciguang | approved |
| 5 | 李小華 | <EMAIL> | employee | ruiguang | pending |

### 工作表 2: 排班申請 (Schedule_Requests)
| A | B | C | D | E | F | G | H |
|---|---|---|---|---|---|---|---|
| 申請ID | 員工ID | 員工姓名 | 申請月份 | 班別類型 | 申請日期 | 狀態 | 審核者 |
| SR001 | 3 | 員工 | 2025-01 | early | 2024-12-15 | pending | - |
| SR002 | 4 | 張小明 | 2025-01 | mid | 2024-12-16 | approved | supervisor |
| SR003 | 5 | 李小華 | 2025-01 | flex | 2024-12-17 | rejected | supervisor |

### 工作表 3: 調班申請 (Shift_Changes)
| A | B | C | D | E | F | G | H | I |
|---|---|---|---|---|---|---|---|---|
| 調班ID | 員工ID | 員工姓名 | 原班別 | 新班別 | 申請日期 | 生效日期 | 狀態 | 審核者 |
| SC001 | 3 | 員工 | early | mid | 2024-12-15 | 2024-12-20 | pending | - |
| SC002 | 4 | 張小明 | mid | early | 2024-12-16 | 2024-12-21 | approved | supervisor |

### 工作表 4: 班別定義 (Shift_Types)
| A | B | C | D |
|---|---|---|---|
| 班別代碼 | 班別名稱 | 開始時間 | 結束時間 |
| early | 早班 | 09:00 | 17:00 |
| mid | 中班 | 13:00 | 21:00 |
| flex | 彈性班 | 01:00 | 03:00 |
| custom | 自訂班別 | - | - |

### 工作表 5: 地點設定 (Locations)
| A | B | C |
|---|---|---|
| 地點代碼 | 地點名稱 | 最少人力 |
| ciguang | 慈光 | 2 |
| ruiguang | 瑞光 | 1 |

## 🔧 設定步驟

### 1. 創建 Google Sheets
1. 前往 [Google Sheets](https://sheets.google.com/)
2. 點擊「空白」創建新試算表
3. 將試算表重新命名為「排班管理系統」

### 2. 創建工作表
1. 在底部標籤右鍵，選擇「插入工作表」
2. 創建以下 5 個工作表：
   - Users
   - Schedule_Requests
   - Shift_Changes
   - Shift_Types
   - Locations

### 3. 輸入範本資料
按照上述表格結構，在每個工作表中輸入對應的標題和範例資料。

### 4. 設定共享權限
1. 點擊右上角「共用」按鈕
2. 在「一般存取權」中選擇「知道連結的任何人」
3. 權限設為「檢視者」（API 只需要讀取權限）
4. 點擊「完成」

### 5. 獲取 Sheets ID
從瀏覽器網址列複製 Sheets ID：
```
https://docs.google.com/spreadsheets/d/[SHEETS_ID]/edit
```
SHEETS_ID 就是 `/d/` 和 `/edit` 之間的那串字符。

## 🧪 測試資料範例

### 完整的測試資料集
```
Users 工作表：
ID	姓名	電子郵件	角色	地點	狀態
1	系統管理員	<EMAIL>	admin	ciguang	approved
2	主管	<EMAIL>	supervisor	ciguang	approved
3	員工	<EMAIL>	employee	ruiguang	approved
4	張小明	<EMAIL>	employee	ciguang	approved
5	李小華	<EMAIL>	employee	ruiguang	pending
6	王大同	<EMAIL>	employee	ciguang	approved
7	陳美玲	<EMAIL>	employee	ruiguang	approved
8	林志明	<EMAIL>	supervisor	ruiguang	approved

Schedule_Requests 工作表：
申請ID	員工ID	員工姓名	申請月份	班別類型	申請日期	狀態	審核者
SR001	3	員工	2025-01	early	2024-12-15	pending	-
SR002	4	張小明	2025-01	mid	2024-12-16	approved	supervisor
SR003	5	李小華	2025-01	flex	2024-12-17	rejected	supervisor
SR004	6	王大同	2025-01	early	2024-12-18	pending	-
SR005	7	陳美玲	2025-01	mid	2024-12-19	approved	supervisor

Shift_Changes 工作表：
調班ID	員工ID	員工姓名	原班別	新班別	申請日期	生效日期	狀態	審核者
SC001	3	員工	early	mid	2024-12-15	2024-12-20	pending	-
SC002	4	張小明	mid	early	2024-12-16	2024-12-21	approved	supervisor
SC003	6	王大同	early	flex	2024-12-17	2024-12-22	rejected	supervisor
```

## 📝 注意事項

1. **API 配額限制**：Google Sheets API 有每日請求限制，免費版通常足夠小型應用使用。

2. **資料格式**：確保日期格式一致（建議使用 YYYY-MM-DD）。

3. **權限設定**：如果是正式環境，建議使用服務帳戶而非 API Key。

4. **資料驗證**：在 Google Sheets 中可以設定資料驗證規則，確保資料品質。

## 🔗 有用的連結

- [Google Sheets API 文件](https://developers.google.com/sheets/api)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Sheets](https://sheets.google.com/)
