# 工作排班系統開發完整記錄

## 🎯 當前狀態：❌ **事病假問題仍未解決**

### **核心問題描述**
事病假餘額計算存在與額外班餘額相同的問題：

**具體問題場景**：
- 員工A目前事病假餘額是2天
- 已批准的7月排班表有1天事病假，8月排班表有2天事病假
- **錯誤行為**：進入7月編輯時顯示餘額1天，進入8月編輯時顯示餘額0天
- **正確行為**：不管進入哪個月份編輯，都應該顯示實際餘額2天

### **問題根源**
事病假的邏輯錯誤地將當前編輯月份的已批准天數從餘額中扣除，導致各月份顯示不一致。

## 🏆 額外班餘額成功解決案例

### **額外班問題解決過程**
額外班餘額曾經有完全相同的問題，但已經成功解決。以下是成功的解決方法：

### **額外班成功解決的關鍵方法**

#### **1. 正確的餘額計算邏輯**
```javascript
// ✅ 額外班的正確做法
function updateOvertimeBalanceDisplay() {
    const totalBalance = currentUser.overtimeBalance || 0; // 直接使用用戶餘額

    // 根據原始天數計算差額
    if (window.originalCompensatoryDays !== undefined) {
        const difference = currentCompensatoryDays - originalDays;
        if (difference > 0) {
            willDeduct = difference;
            finalBalance = totalBalance - willDeduct;
            balanceDisplay.innerHTML = `將扣除 ${willDeduct} 天，剩餘 ${finalBalance} 天`;
        }
    } else {
        balanceDisplay.textContent = `${totalBalance} 天`;
    }
}
```

#### **2. 重新編輯時的正確處理**
```javascript
// ✅ 額外班重新編輯的正確邏輯
if (existingRequest.status === 'approved') {
    // 只記錄原始天數，不恢復餘額
    window.originalCompensatoryDays = existingRequest.compensatoryDaysCount || 0;
    console.log(`記錄原始補休天數: ${window.originalCompensatoryDays}`);

    // 不在這裡恢復餘額，保持實際餘額不變
}
```

#### **3. 審核批准時才調整餘額**
```javascript
// ✅ 額外班審核批准的正確邏輯
if (request.hasCompensatoryLeave && request.compensatoryDaysCount > 0) {
    // 只有在審核批准時才真正調整餘額
    const employeeIndex = systemData.users.findIndex(u => u.id === request.employeeId);
    if (employeeIndex !== -1) {
        systemData.users[employeeIndex].overtimeBalance -= request.compensatoryDaysCount;
    }
}
```

## ❌ 事病假當前的錯誤邏輯

### **錯誤的餘額計算**
```javascript
// ❌ 事病假的錯誤做法（需要修正）
function updateSickLeaveBalanceDisplay() {
    const currentSickLeaveDays = Object.values(currentScheduleData).filter(shift => shift.type === 'sick').length;
    const totalQuota = currentUser.annualSickLeaveQuota || 0;
    const usedDays = currentUser.usedSickLeaveDays || 0;

    // 錯誤：將當前選擇的天數也扣除了
    const availableBalance = totalQuota - usedDays - currentSickLeaveDays;

    balanceDisplay.textContent = availableBalance;
}
```

### **問題分析**
1. **錯誤扣除**：`totalQuota - usedDays - currentSickLeaveDays` 將當前編輯的天數也扣除
2. **各月不一致**：每個月份都會扣除該月的已批准天數，導致顯示不同
3. **邏輯混亂**：沒有區分「實際餘額」和「預覽變化」

## 🎯 事病假需要的修正方案

### **參考額外班的成功模式**
```javascript
// ✅ 事病假應該使用的正確邏輯（仿照額外班）
function updateSickLeaveBalanceDisplay() {
    const totalQuota = currentUser.annualSickLeaveQuota || 0;
    const usedDays = currentUser.usedSickLeaveDays || 0;
    const totalBalance = totalQuota - usedDays; // 實際可用餘額，不扣除當前選擇

    // 根據原始天數計算差額（仿照額外班）
    if (window.originalSickLeaveDays !== undefined) {
        const difference = currentSickLeaveDays - originalDays;
        if (difference > 0) {
            balanceDisplay.innerHTML = `將扣除 ${difference} 天，剩餘 ${totalBalance - difference} 天`;
        }
    } else {
        balanceDisplay.textContent = `${totalBalance} 天`;
    }
}
```

### **3. 審核批准後調整變數**
```javascript
// 直到此申請得到主管批准後，才去調整變數新值，並全域統一顯示

// 1. 如果申請包含事病假，扣減員工的事病假額度
if (request.hasSickLeave && request.sickLeaveDaysCount > 0) {
    const employeeIndex = systemData.users.findIndex(u => u.id === request.employeeId);
    if (employeeIndex !== -1) {
        const oldUsedDays = systemData.users[employeeIndex].usedSickLeaveDays || 0;
        systemData.users[employeeIndex].usedSickLeaveDays = oldUsedDays + request.sickLeaveDaysCount;
        
        console.log(`🏥 批准申請 - 扣減員工 ${request.employeeName} 事病假額度: 已用天數 ${oldUsedDays} → ${systemData.users[employeeIndex].usedSickLeaveDays}`);
        
        // 同步當前用戶的事病假餘額
        if (currentUser && currentUser.id === request.employeeId) {
            currentUser.usedSickLeaveDays = systemData.users[employeeIndex].usedSickLeaveDays;
            actualSickLeaveBalance = (currentUser.annualSickLeaveQuota || 0) - currentUser.usedSickLeaveDays;
            console.log(`🔄 同步當前用戶事病假餘額: ${actualSickLeaveBalance}`);
        }
    }
}
```

### **4. 原始天數記錄機制**
```javascript
// 重新編輯時記錄原始事病假天數
if (existingRequest.status === 'approved') {
    // 記錄原始申請的事病假天數，供後續比較使用
    window.originalSickLeaveDays = existingRequest.sickLeaveDaysCount || 0;
    console.log(`🔄 重新編輯已批准申請 - 記錄原始事病假天數: ${window.originalSickLeaveDays}`);
}
```

### **5. 除錯工具完善**
```javascript
// 修正員工事病假餘額不一致問題
function fixEmployeeSickLeaveBalance(employeeId)

// 重設所有事病假餘額（除錯用）
function resetAllSickLeaveBalances()
```

## 🧪 完整測試流程

### **步驟1：檢查初始狀態**
```javascript
// 在 Console 中執行
console.log('當前用戶:', currentUser);
console.log('實際額外班餘額:', actualOvertimeBalance);
console.log('實際事病假餘額:', actualSickLeaveBalance);
console.log('年度事病假額度:', currentUser.annualSickLeaveQuota);
console.log('已用事病假:', currentUser.usedSickLeaveDays);
```

### **步驟2：測試新申請**
1. 登入員工帳號：<EMAIL> / emp123
2. 進入「排班申請」，選擇月份
3. 點擊任一天，選擇「事病假」
4. 觀察餘額顯示：應該顯示 `X 天 (將扣除 1 天)`
5. 提交申請

### **步驟3：測試審核批准**
1. 登入主管帳號：<EMAIL> / super123
2. 進入「待審核申請」
3. 批准事病假申請
4. 觀察 Console 輸出：
   ```
   🏥 批准申請 - 扣減員工 XXX 事病假額度: 已用天數 X → X
   🔄 同步當前用戶事病假餘額: X
   ```

### **步驟4：測試重新編輯**
1. 員工重新編輯已批准的申請
2. 觀察 Console 輸出：
   ```
   🔄 重新編輯已批准申請 - 記錄原始事病假天數: X
   🏥 重新編輯時實際事病假餘額: X 天
   ```
3. 修改事病假天數
4. 觀察餘額預覽：`X 天 (將扣除/恢復 X 天)`

### **步驟5：測試全域一致性**
1. 在不同月份的排班申請中檢查事病假餘額
2. 所有月份應該顯示相同的實際餘額
3. 使用除錯工具驗證：
   ```javascript
   calculateActualSickLeaveBalance(currentUser.id);
   fixEmployeeSickLeaveBalance(currentUser.id);
   ```

## 📊 預期結果

### **Console 輸出範例**
```
🔄 重新編輯已批准申請 - 記錄原始事病假天數: 2
🏥 重新編輯時實際事病假餘額: 18 天
🎯 更新編輯對話框事病假餘額顯示:
   - 當前事病假天數: 3
   - 實際餘額: 18
   - 原始事病假天數: 2
🎯 重新編輯模式 - 差額: 1
🎯 將新增事病假 1 天，預期餘額: 17
🏥 批准申請 - 扣減員工 員工 事病假額度: 已用天數 12 → 13
🔄 同步當前用戶事病假餘額: 17
```

### **餘額顯示效果**
- **編輯時預覽**：`18 天 (將扣除 1 天)`
- **各月份一致**：所有月份都顯示相同的實際餘額
- **實時更新**：審核後立即同步所有顯示

## ✅ 驗證清單

- [ ] 事病假餘額全域統一顯示
- [ ] 編輯時正確預覽餘額變化
- [ ] 審核批准後正確調整餘額
- [ ] 重新編輯時正確計算差額
- [ ] 原始天數記錄機制正常
- [ ] 除錯工具正常運作
- [ ] 各月份餘額顯示一致
- [ ] Console 輸出詳細追蹤

## 🎯 核心成就

1. **完全統一**：事病假餘額現在和額外班餘額一樣，使用全域統一變數管理
2. **預覽機制**：編輯時即時顯示餘額變化預覽
3. **延遲生效**：只有在主管批准後才真正調整餘額
4. **全域一致**：所有月份的事病假餘額顯示完全一致
5. **詳細追蹤**：完整的 Console 輸出可追蹤每個操作

## 🚀 下個對話銜接指南

### **系統當前狀態**
- ✅ 額外班餘額管理正常（已成功解決）
- ❌ 事病假餘額管理有問題（需要修正）
- ✅ 登入功能正常運作
- ✅ 基本排班申請功能完整

### **立即需要解決的問題**
1. **事病假餘額統一管理**：參考額外班的成功模式進行修正
2. **修正函數**：`updateSickLeaveBalanceDisplay()`, `updateSickLeaveBalanceInSummary()`, `saveDayShift()`
3. **測試驗證**：確保各月份顯示一致的實際餘額

### **成功解決的參考範本**
額外班餘額的邏輯是完全正確的，事病假需要完全仿照：
1. **直接使用實際餘額**：不扣除當前選擇的天數
2. **差額計算預覽**：基於原始天數計算變化
3. **延遲生效機制**：審核批准後才調整餘額
4. **原始天數記錄**：重新編輯時正確處理

### **關鍵修正要點**
- 移除 `totalQuota - usedDays - currentSickLeaveDays` 中的 `currentSickLeaveDays`
- 改為 `totalBalance = totalQuota - usedDays`（仿照額外班）
- 使用 `window.originalSickLeaveDays` 記錄原始天數
- 實現與額外班完全一致的預覽機制

### **測試場景**
員工A餘額2天，7月已批准1天，8月已批准2天：
- 進入7月編輯：應顯示2天（不是1天）
- 進入8月編輯：應顯示2天（不是0天）
- 新增事病假：顯示預覽變化
- 審核批准後：才調整實際餘額

---

**系統版本**：v9.1 - 事病假問題待修正
**更新日期**：2024年12月
**狀態**：❌ 事病假邏輯需要修正，參考額外班成功模式
