'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Clock, FileText, Settings, LogOut, Plus, Edit, CheckCircle, XCircle } from 'lucide-react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'employee';
  location: 'ciguang' | 'ruiguang';
  approved: boolean;
}

interface ScheduleRequest {
  id: string;
  month: string;
  shifts: {
    date: string;
    type: 'early' | 'mid' | 'flex' | 'custom';
    startTime?: string;
    endTime?: string;
    location: 'ciguang' | 'ruiguang';
  }[];
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  submittedAt?: string;
  approvedAt?: string;
  rejectedReason?: string;
}

export default function EmployeeDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [scheduleRequests, setScheduleRequests] = useState<ScheduleRequest[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 檢查用戶登入狀態
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== 'employee') {
      router.push('/login');
      return;
    }

    setUser(parsedUser);
    loadScheduleRequests();
  }, [router]);

  const loadScheduleRequests = async () => {
    setLoading(true);
    try {
      // 模擬載入排班申請資料
      const mockRequests: ScheduleRequest[] = [
        {
          id: '1',
          month: '2024-12',
          shifts: [
            { date: '2024-12-01', type: 'early', location: 'ruiguang' },
            { date: '2024-12-02', type: 'mid', location: 'ruiguang' },
            { date: '2024-12-03', type: 'flex', startTime: '10:00', endTime: '18:00', location: 'ruiguang' }
          ],
          status: 'approved',
          submittedAt: '2024-11-25',
          approvedAt: '2024-11-26'
        },
        {
          id: '2',
          month: '2025-01',
          shifts: [
            { date: '2025-01-01', type: 'early', location: 'ruiguang' },
            { date: '2025-01-02', type: 'mid', location: 'ruiguang' }
          ],
          status: 'submitted',
          submittedAt: '2024-12-01'
        }
      ];
      setScheduleRequests(mockRequests);
    } catch (error) {
      console.error('載入排班申請失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    router.push('/');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'submitted': return 'text-yellow-600 bg-yellow-100';
      case 'draft': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return '已核准';
      case 'rejected': return '已拒絕';
      case 'submitted': return '待審核';
      case 'draft': return '草稿';
      default: return '未知';
    }
  };

  const getShiftTypeText = (type: string) => {
    switch (type) {
      case 'early': return '早班 (9:00-17:00)';
      case 'mid': return '中班 (13:00-21:00)';
      case 'flex': return '彈性班';
      case 'custom': return '自訂班次';
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">員工排班系統</h1>
                <p className="text-sm text-gray-600">歡迎回來，{user?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {user?.location === 'ciguang' ? '慈光' : '瑞光'}
              </span>
              <button
                onClick={handleLogout}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <LogOut className="h-5 w-5 mr-1" />
                登出
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <button
              onClick={() => router.push('/employee/schedule/new')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow border-2 border-dashed border-gray-300 hover:border-indigo-400"
            >
              <Plus className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">新增排班申請</h3>
              <p className="text-sm text-gray-600 text-center mt-1">提交下月排班需求</p>
            </button>

            <button
              onClick={() => router.push('/employee/shift-change')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <Edit className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">調班申請</h3>
              <p className="text-sm text-gray-600 text-center mt-1">申請臨時調班</p>
            </button>

            <button
              onClick={() => router.push('/employee/reports')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">工時報表</h3>
              <p className="text-sm text-gray-600 text-center mt-1">查看工時統計</p>
            </button>
          </div>

          {/* Schedule Requests */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">我的排班申請</h2>
            </div>
            <div className="divide-y divide-gray-200">
              {scheduleRequests.length === 0 ? (
                <div className="px-6 py-8 text-center">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">尚無排班申請</p>
                  <button
                    onClick={() => router.push('/employee/schedule/new')}
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    新增申請
                  </button>
                </div>
              ) : (
                scheduleRequests.map((request) => (
                  <div key={request.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {request.status === 'approved' ? (
                            <CheckCircle className="h-6 w-6 text-green-600" />
                          ) : request.status === 'rejected' ? (
                            <XCircle className="h-6 w-6 text-red-600" />
                          ) : (
                            <Clock className="h-6 w-6 text-yellow-600" />
                          )}
                        </div>
                        <div className="ml-4">
                          <h3 className="text-lg font-medium text-gray-900">
                            {new Date(request.month).toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' })} 排班
                          </h3>
                          <p className="text-sm text-gray-600">
                            共 {request.shifts.length} 個班次
                            {request.submittedAt && ` • 提交於 ${new Date(request.submittedAt).toLocaleDateString('zh-TW')}`}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {getStatusText(request.status)}
                        </span>
                        <button
                          onClick={() => router.push(`/employee/schedule/${request.id}`)}
                          className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                        >
                          查看詳情
                        </button>
                      </div>
                    </div>
                    {request.rejectedReason && (
                      <div className="mt-2 p-3 bg-red-50 rounded-md">
                        <p className="text-sm text-red-700">
                          <strong>拒絕原因：</strong>{request.rejectedReason}
                        </p>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
