# 工作排班系統開發記錄 - 額外班餘額問題

## 📋 專案基本資訊

### **專案名稱**：工作排班系統
### **主要檔案**：`schedule-app-simple.html`
### **開發者**：wenbin
### **當前狀態**：額外班餘額計算邏輯修正中

## 🔍 當前主要問題

### **核心問題**：假單審批後額外班餘額不正確
- **現象**：主管批准包含補休的申請後，員工的額外班餘額計算錯誤
- **影響**：員工重新編輯時餘額顯示不正確，系統資料不一致

## 📊 系統架構概述

### **用戶角色**
- **管理員**：系統管理、用戶管理、額外班設定
- **主管**：審核排班申請、設定員工額外班
- **員工**：提交排班申請、查看額外班餘額

### **核心功能模組**
1. **排班申請系統**：月曆式排班申請與審核
2. **額外班管理**：主管設定、員工使用補休
3. **事病假管理**：年度額度管理
4. **審核系統**：主管審核排班申請
5. **通知系統**：系統通知與提醒

### **資料結構**
```javascript
systemData = {
    users: [],              // 用戶資料
    scheduleRequests: [],   // 排班申請記錄
    overtimeRecords: [],    // 額外班記錄
    notifications: []       // 通知記錄
}
```

## 🔧 額外班邏輯設計

### **額外班產生**
- 主管可為員工加註額外班（0.5天或1天）
- 記錄在 `overtimeRecords` 中
- 員工的 `overtimeBalance` = 所有記錄的總和

### **補休使用**
- 員工在排班申請中可選擇「補休」
- 補休需要消耗額外班餘額
- 主管批准後扣減員工的 `overtimeBalance`

### **重新編輯邏輯**
- 員工可重新編輯已批准的申請
- 系統需要計算補休天數的差額
- 增加補休 → 扣減餘額，減少補休 → 恢復餘額

## 🐛 已知問題與修正歷程

### **問題1：主管批准後員工月班表回到預設值** ✅ **已解決**
- **原因**：員工編輯時沒有載入已批准的排班資料
- **解決**：直接從已批准的 `scheduleRequests` 載入資料

### **問題2：額外班累計不一致** ⚠️ **部分解決**
- **原因**：多處重複計算額外班餘額
- **解決**：統一使用 `recalculateEmployeeOvertimeBalance()` 函數

### **問題3：重複扣除額外班餘額** ⚠️ **修正中**
- **原因**：提交申請時和主管批准時都處理餘額
- **嘗試解決**：只在主管批准時處理餘額
- **當前狀態**：仍有問題，需要進一步修正

## 🔍 當前問題詳細分析

### **問題現象**
1. 員工申請包含補休的假單
2. 主管批准申請
3. 員工的額外班餘額計算錯誤
4. 重新編輯時顯示的餘額不正確

### **可能原因**
1. **重複扣除**：系統在多個地方同時處理餘額
2. **計算邏輯錯誤**：差額計算不正確
3. **資料同步問題**：不同地方的餘額顯示不一致
4. **舊記錄干擾**：重新編輯時舊申請記錄影響計算

### **需要檢查的關鍵函數**
- `processApproval()` - 主管審核處理
- `submitScheduleRequest()` - 員工提交申請
- `recalculateEmployeeOvertimeBalance()` - 餘額重新計算
- `updateOvertimeBalanceInSummary()` - 餘額顯示更新

## 📝 測試場景

### **基本測試流程**
1. **設定員工額外班**：10天
2. **員工申請補休**：3天
3. **主管批准申請**：檢查餘額是否變成7天
4. **員工重新編輯**：
   - 增加補休到5天 → 餘額應該變成5天
   - 減少補休到1天 → 餘額應該變成9天
   - 完全取消補休 → 餘額應該變成10天

### **除錯指令**
```javascript
// 檢查系統狀態
debugSystemData();

// 檢查特定員工的額外班記錄
systemData.overtimeRecords.filter(r => r.employeeId === '3');

// 手動重新計算
recalculateEmployeeOvertimeBalance('3');
```

## 🎯 下一步行動計畫

### **立即需要修正**
1. **深度檢查審核邏輯**：確認主管批准時的餘額處理
2. **驗證計算公式**：確保差額計算正確
3. **測試所有場景**：新申請、重新編輯、取消補休
4. **資料一致性檢查**：確保所有地方顯示相同餘額

### **建議的修正方向**
1. **簡化邏輯**：只在一個地方處理額外班餘額
2. **增強除錯**：添加更詳細的 Console 輸出
3. **資料驗證**：添加餘額計算的驗證機制
4. **測試覆蓋**：確保所有操作路徑都正確

## 📁 相關檔案

### **主要檔案**
- `schedule-app-simple.html` - 主系統檔案
- `工作排班系統開發備忘錄.md` - 完整開發計畫

### **關鍵程式碼區段**
- 第2719-2754行：主管審核時的額外班處理
- 第2150-2158行：員工提交申請時的處理
- 第3972-4001行：額外班餘額重新計算函數
- 第1962-2000行：餘額顯示更新函數

---

**記錄時間**：2024年12月
**下次對話重點**：修正假單審批後額外班餘額計算錯誤的問題
