'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Clock, Users, FileText, LogOut, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'employee';
  location: 'ciguang' | 'ruiguang';
  approved: boolean;
}

interface PendingRequest {
  id: string;
  employeeName: string;
  employeeLocation: 'ciguang' | 'ruiguang';
  type: 'schedule' | 'shift-change';
  month?: string;
  requestDate?: string;
  shiftsCount?: number;
  submittedAt: string;
  priority: 'high' | 'medium' | 'low';
}

export default function SupervisorDashboard() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [pendingRequests, setPendingRequests] = useState<PendingRequest[]>([]);
  const [stats, setStats] = useState({
    totalEmployees: 0,
    pendingApprovals: 0,
    thisMonthApproved: 0,
    staffingToday: { ciguang: 0, ruiguang: 0 }
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 檢查用戶登入狀態
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== 'supervisor') {
      router.push('/login');
      return;
    }

    setUser(parsedUser);
    loadDashboardData();
  }, [router]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 模擬載入儀表板資料
      const mockPendingRequests: PendingRequest[] = [
        {
          id: '1',
          employeeName: '張小明',
          employeeLocation: 'ciguang',
          type: 'schedule',
          month: '2025-01',
          shiftsCount: 15,
          submittedAt: '2024-12-01',
          priority: 'high'
        },
        {
          id: '2',
          employeeName: '李小華',
          employeeLocation: 'ruiguang',
          type: 'shift-change',
          requestDate: '2024-12-15',
          submittedAt: '2024-12-10',
          priority: 'medium'
        },
        {
          id: '3',
          employeeName: '王小美',
          employeeLocation: 'ciguang',
          type: 'schedule',
          month: '2025-01',
          shiftsCount: 12,
          submittedAt: '2024-11-30',
          priority: 'low'
        }
      ];

      const mockStats = {
        totalEmployees: 8,
        pendingApprovals: mockPendingRequests.length,
        thisMonthApproved: 12,
        staffingToday: { ciguang: 2, ruiguang: 1 }
      };

      setPendingRequests(mockPendingRequests);
      setStats(mockStats);
    } catch (error) {
      console.error('載入儀表板資料失敗:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('user');
    router.push('/');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '未知';
    }
  };

  const getRequestTypeText = (type: string) => {
    switch (type) {
      case 'schedule': return '排班申請';
      case 'shift-change': return '調班申請';
      default: return '未知申請';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">主管管理系統</h1>
                <p className="text-sm text-gray-600">歡迎回來，{user?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {user?.location === 'ciguang' ? '慈光' : '瑞光'}
              </span>
              <button
                onClick={handleLogout}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <LogOut className="h-5 w-5 mr-1" />
                登出
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">總員工數</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalEmployees}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-8 w-8 text-yellow-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">待審核申請</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.pendingApprovals}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-green-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">本月已核准</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.thisMonthApproved}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-8 w-8 text-blue-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">今日人力</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        慈光{stats.staffingToday.ciguang} / 瑞光{stats.staffingToday.ruiguang}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <button
              onClick={() => router.push('/supervisor/approvals')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">審核申請</h3>
              <p className="text-sm text-gray-600 text-center mt-1">處理排班與調班申請</p>
            </button>

            <button
              onClick={() => router.push('/supervisor/schedule-overview')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <Calendar className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">排班總覽</h3>
              <p className="text-sm text-gray-600 text-center mt-1">查看整體排班狀況</p>
            </button>

            <button
              onClick={() => router.push('/supervisor/reports')}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 text-center">報表管理</h3>
              <p className="text-sm text-gray-600 text-center mt-1">生成各類統計報表</p>
            </button>
          </div>

          {/* Pending Requests */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">待處理申請</h2>
            </div>
            <div className="divide-y divide-gray-200">
              {pendingRequests.length === 0 ? (
                <div className="px-6 py-8 text-center">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">目前沒有待處理的申請</p>
                </div>
              ) : (
                pendingRequests.map((request) => (
                  <div key={request.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {request.type === 'schedule' ? (
                            <Calendar className="h-6 w-6 text-blue-600" />
                          ) : (
                            <Clock className="h-6 w-6 text-orange-600" />
                          )}
                        </div>
                        <div className="ml-4">
                          <h3 className="text-lg font-medium text-gray-900">
                            {request.employeeName} - {getRequestTypeText(request.type)}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {request.employeeLocation === 'ciguang' ? '慈光' : '瑞光'}
                            {request.month && ` • ${new Date(request.month).toLocaleDateString('zh-TW', { year: 'numeric', month: 'long' })}`}
                            {request.shiftsCount && ` • ${request.shiftsCount} 個班次`}
                            {request.requestDate && ` • 申請日期: ${new Date(request.requestDate).toLocaleDateString('zh-TW')}`}
                          </p>
                          <p className="text-xs text-gray-500">
                            提交於 {new Date(request.submittedAt).toLocaleDateString('zh-TW')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                          優先度: {getPriorityText(request.priority)}
                        </span>
                        <button
                          onClick={() => router.push(`/supervisor/request/${request.id}`)}
                          className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                        >
                          處理
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
