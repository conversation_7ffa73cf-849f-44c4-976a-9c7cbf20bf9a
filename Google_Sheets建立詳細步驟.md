# Google Sheets 建立詳細步驟

## 🎯 目標
在您的 Google Workspace 中建立「工作排班管理系統」Google Sheets 資料庫

## 📋 步驟 1：建立 Google Sheets

### **方法 A：直接在 Google Sheets 建立（推薦）**

1. **開啟 Google Sheets**
   - 前往：https://sheets.google.com
   - 使用您的公司 Google Workspace 帳戶登入

2. **建立新試算表**
   - 點擊左上角「建立」或「+」按鈕
   - 選擇「空白試算表」

3. **重新命名試算表**
   - 點擊左上角「未命名的試算表」
   - 改名為：`工作排班管理系統`

### **方法 B：上傳 Excel 檔案轉換**

1. **建立 Excel 檔案**
   - 開啟 Microsoft Excel
   - 建立新工作簿
   - 儲存為：`工作排班管理系統.xlsx`

2. **上傳到 Google Drive**
   - 前往：https://drive.google.com
   - 點擊「新增」→「檔案上傳」
   - 選擇剛建立的 Excel 檔案

3. **轉換為 Google Sheets**
   - 在 Google Drive 中找到上傳的檔案
   - 右鍵點擊 → 「開啟工具」→ 「Google 試算表」
   - 系統會自動轉換為 Google Sheets 格式

## 📊 步驟 2：建立工作表結構

### **工作表 1：Users（用戶資料）**

1. **重新命名工作表**
   - 右鍵點擊底部的「工作表1」
   - 選擇「重新命名」
   - 改名為：`Users`

2. **設定標題行（第1行）**
   請在第1行依序輸入以下欄位：

   | A | B | C | D | E | F | G | H |
   |---|---|---|---|---|---|---|---|
   | id | name | email | password | role | location | department | lineId |

   | I | J | K | L | M | N | O | P |
   |---|---|---|---|---|---|---|---|
   | approved | monthlyLeaveDays | overtimeBalance | annualSickLeaveQuota | sickLeaveBalance | usedSickLeaveDays | weeklySchedule | registrationDate |

   | Q | R |
   |---|---|
   | lastLoginDate | isActive |

3. **格式化標題行**
   - 選取第1行（A1:R1）
   - 設定為粗體
   - 背景色設為淺藍色
   - 凍結第1行：「檢視」→「凍結」→「1列」

### **工作表 2：ScheduleRequests（排班申請）**

1. **新增工作表**
   - 點擊底部的「+」號
   - 重新命名為：`ScheduleRequests`

2. **設定標題行**
   | A | B | C | D | E | F | G | H |
   |---|---|---|---|---|---|---|---|
   | id | employeeId | employeeName | month | status | scheduleData | hasCompensatoryLeave | compensatoryDaysCount |

   | I | J | K | L | M | N | O | P |
   |---|---|---|---|---|---|---|---|
   | hasSickLeave | sickLeaveDaysCount | submittedAt | approver | approvedAt | comments | originalCompensatoryDays | originalSickLeaveDays |

### **工作表 3：OvertimeRecords（額外班記錄）**

1. **新增工作表**
   - 重新命名為：`OvertimeRecords`

2. **設定標題行**
   | A | B | C | D | E | F | G | H | I | J |
   |---|---|---|---|---|---|---|---|---|---|
   | id | employeeId | employeeName | date | duration | type | addedBy | addedDate | reason | isApproved |

### **工作表 4：Notifications（通知記錄）**

1. **新增工作表**
   - 重新命名為：`Notifications`

2. **設定標題行**
   | A | B | C | D | E | F | G | H | I | J |
   |---|---|---|---|---|---|---|---|---|---|
   | id | type | title | message | targetUserId | isRead | createdAt | createdBy | priority | category |

### **工作表 5：EmployeeSchedules（員工排班）**

1. **新增工作表**
   - 重新命名為：`EmployeeSchedules`

2. **設定標題行**
   | A | B | C | D | E | F | G | H | I |
   |---|---|---|---|---|---|---|---|---|
   | id | employeeId | employeeName | month | scheduleData | totalWorkDays | totalLeaveDays | createdAt | updatedAt |

### **工作表 6：ShiftChanges（調班記錄）**

1. **新增工作表**
   - 重新命名為：`ShiftChanges`

2. **設定標題行**
   | A | B | C | D | E | F | G | H | I | J | K | L |
   |---|---|---|---|---|---|---|---|---|---|---|---|
   | id | employeeId | employeeName | originalDate | newDate | originalShift | newShift | reason | status | requestedAt | approver | approvedAt |

### **工作表 7：SystemSettings（系統設定）**

1. **新增工作表**
   - 重新命名為：`SystemSettings`

2. **設定標題行**
   | A | B | C | D | E | F |
   |---|---|---|---|---|---|
   | settingKey | settingValue | category | description | updatedAt | updatedBy |

### **工作表 8：ActivityLogs（操作日誌）**

1. **新增工作表**
   - 重新命名為：`ActivityLogs`

2. **設定標題行**
   | A | B | C | D | E | F | G | H | I | J |
   |---|---|---|---|---|---|---|---|---|---|
   | id | userId | userName | action | targetType | targetId | details | ipAddress | userAgent | timestamp |

## 🔧 步驟 3：格式化和設定

### **統一格式設定**
對每個工作表執行以下操作：

1. **標題行格式化**
   - 選取第1行
   - 設定為粗體
   - 背景色：淺藍色（#E3F2FD）
   - 文字置中對齊

2. **凍結標題行**
   - 「檢視」→「凍結」→「1列」

3. **調整欄寬**
   - 雙擊欄位分隔線自動調整寬度
   - 或手動調整重要欄位寬度

### **資料驗證設定（可選）**

1. **Users 工作表 - role 欄位（E欄）**
   - 選取 E2:E1000
   - 「資料」→「資料驗證」
   - 條件：清單項目
   - 項目：`admin,supervisor,employee`

2. **ScheduleRequests 工作表 - status 欄位（E欄）**
   - 選取 E2:E1000
   - 條件：清單項目
   - 項目：`pending,approved,rejected`

## 📍 步驟 4：取得 Sheets ID

### **取得 Google Sheets ID**

1. **開啟建立好的 Google Sheets**
2. **查看瀏覽器網址列**
   - 網址格式：`https://docs.google.com/spreadsheets/d/SHEETS_ID/edit#gid=0`
   - 複製 `/d/` 和 `/edit` 之間的字串

3. **範例**
   ```
   網址：https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit#gid=0
   
   Sheets ID：1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
   ```

4. **記錄 Sheets ID**
   - 將 ID 複製並保存
   - 稍後在 Google Apps Script 中會用到

## ✅ 步驟 5：驗證設定

### **檢查清單**
- [ ] 試算表名稱：`工作排班管理系統`
- [ ] 共8個工作表，名稱正確
- [ ] 每個工作表都有正確的標題行
- [ ] 標題行已格式化（粗體、背景色）
- [ ] 已凍結標題行
- [ ] 已取得 Sheets ID

### **測試資料（可選）**
您可以在 Users 工作表中新增一筆測試資料：

| id | name | email | password | role | location | department | lineId | approved | monthlyLeaveDays | overtimeBalance | annualSickLeaveQuota | sickLeaveBalance | usedSickLeaveDays | weeklySchedule | registrationDate | lastLoginDate | isActive |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| admin_001 | 系統管理員 | <EMAIL> | admin123 | admin | ciguang | admin |  | TRUE | 6 | 0 | 20 | 20 | 0 | {} | 2024-12-01T00:00:00Z |  | TRUE |

## 🔐 步驟 6：設定共享權限

### **設定 Google Sheets 權限**

1. **開啟共享設定**
   - 點擊右上角「共用」按鈕

2. **設定一般存取權**
   - 選擇「貴機構中的任何人」
   - 權限：「檢視者」（重要：不要設為編輯者）

3. **新增特定使用者**
   - 輸入需要管理權限的使用者電子郵件
   - 設定為「編輯者」權限

4. **複製共享連結**
   - 點擊「複製連結」
   - 保存此連結供後續使用

## 📝 完成確認

完成以上步驟後，您應該有：

1. ✅ **Google Sheets 資料庫**：包含8個正確設定的工作表
2. ✅ **Sheets ID**：用於 Google Apps Script 連接
3. ✅ **共享權限**：適當的訪問控制設定
4. ✅ **測試資料**：至少一筆管理員帳戶資料

## 🚀 下一步

完成 Google Sheets 建立後，請告訴我：

1. **您的 Sheets ID**
2. **是否成功建立了所有8個工作表**
3. **是否遇到任何問題**

然後我們就可以繼續進行下一步：建立 Google Apps Script 後端！

---

**💡 小提示**
- 建議在公司網路環境下操作，確保穩定連線
- 如果遇到權限問題，請聯繫您的 IT 管理員
- 建立過程中可以隨時儲存，Google Sheets 會自動保存變更
