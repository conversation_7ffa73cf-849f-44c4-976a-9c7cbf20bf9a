# 額外班餘額修正測試指南

## 🎯 修正內容概述

### **主要修正**
1. **實際餘額管理**：新增 `actualOvertimeBalance` 全域變數，統一管理真實可用餘額
2. **修正重新編輯邏輯**：重新編輯時不再錯誤恢復餘額，而是基於實際餘額計算
3. **統一餘額顯示**：所有餘額顯示都使用實際餘額，避免顯示已扣除的錯誤值
4. **資料同步機制**：確保 `actualOvertimeBalance`、`currentUser.overtimeBalance` 與 `systemData.users[].overtimeBalance` 三者同步
5. **增強除錯輸出**：添加詳細的 Console 輸出追蹤餘額變化
6. **除錯工具完善**：提供完整的系統狀態檢查和修正工具

### **關鍵修正點**
- 第282行：新增 `actualOvertimeBalance` 全域變數
- 第493-500行：登入時計算並設定實際餘額
- 第888-925行：重新編輯時的邏輯修正（不再錯誤恢復餘額）
- 第1406-1470行：編輯對話框餘額顯示修正
- 第2029-2078行：排班申請頁面餘額顯示修正
- 第2115-2132行：提交申請時的餘額檢查修正
- 第2773-2809行：審核處理時的餘額同步
- 第4121-4160行：實際餘額計算函數

## 🧪 測試場景

### **場景1：新申請補休**
```
初始狀態：員工額外班餘額 10 天
操作步驟：
1. 員工申請下月排班，包含 3 天補休
2. 主管批准申請
預期結果：員工餘額變成 7 天
```

### **場景2：重新編輯增加補休**
```
初始狀態：員工已有 3 天補休被批准，餘額 7 天
操作步驟：
1. 員工重新編輯該月排班，改為 5 天補休
2. 主管批准申請
預期結果：員工餘額變成 5 天（扣減 2 天差額）
```

### **場景3：重新編輯減少補休**
```
初始狀態：員工已有 5 天補休被批准，餘額 5 天
操作步驟：
1. 員工重新編輯該月排班，改為 2 天補休
2. 主管批准申請
預期結果：員工餘額變成 8 天（恢復 3 天差額）
```

### **場景4：完全取消補休**
```
初始狀態：員工已有 2 天補休被批准，餘額 8 天
操作步驟：
1. 員工重新編輯該月排班，改為 0 天補休
2. 主管批准申請
預期結果：員工餘額變成 10 天（恢復 2 天）
```

## 🔍 測試步驟

### **準備工作**
1. 開啟瀏覽器開發者工具（F12）
2. 切換到 Console 標籤
3. 登入系統（建議使用員工帳號：<EMAIL> / emp123）

### **測試前檢查**
```javascript
// 檢查系統狀態
debugSystemData();

// 檢查當前用戶餘額
console.log('當前用戶餘額:', currentUser.overtimeBalance);
```

### **執行測試**
1. **設定初始額外班**（使用主管帳號）
   - 登入主管帳號：<EMAIL> / super123
   - 進入「額外班管理」
   - 為員工設定額外班（建議設定 10 天）

2. **測試新申請**
   - 登入員工帳號：<EMAIL> / emp123
   - 新增排班申請，包含補休
   - 觀察 Console 輸出
   - 主管批准申請
   - 檢查餘額變化

3. **測試重新編輯**
   - 員工重新編輯已批准的申請
   - 修改補休天數
   - 觀察 Console 輸出
   - 主管批准申請
   - 檢查餘額變化

### **驗證結果**
```javascript
// 檢查最終狀態
debugSystemData();

// 手動驗證特定員工餘額
fixEmployeeOvertimeBalance('3'); // 員工ID

// 重置所有餘額（如果發現問題）
resetAllOvertimeBalances();
```

## 📊 Console 輸出說明

### **關鍵輸出標識**
- 🔄：重新編輯相關操作
- 🆕：新申請相關操作
- ✅：成功操作
- ⚠️：警告或不一致
- 🔍：除錯資訊
- 📝：提交申請
- 📊：餘額計算

### **重要輸出範例**
```
🔄 重新編輯已批准申請 - 記錄原始補休天數: 3
📝 重新提交申請:
   - 原始補休: 3 天
   - 新補休: 5 天
   - 差額: 2 天
🔄 重新編輯申請處理:
   - 原始補休: 3 天
   - 新補休: 5 天
   - 差額: 2 天
✅ 批准重新編輯申請 - 新增補休 2 天
   員工 員工 額外班餘額: 7 → 5
```

## 🛠️ 除錯工具使用

### **系統狀態檢查**
```javascript
debugSystemData(); // 完整系統狀態檢查
```

### **修正特定員工餘額**
```javascript
fixEmployeeOvertimeBalance('3'); // 修正員工ID為3的餘額
```

### **重置所有餘額**
```javascript
resetAllOvertimeBalances(); // 重新計算所有員工餘額
```

### **手動計算餘額**
```javascript
recalculateEmployeeOvertimeBalance('3'); // 重新計算特定員工餘額
```

## ⚠️ 注意事項

1. **測試順序**：建議按照場景順序測試，避免資料混亂
2. **Console 監控**：全程監控 Console 輸出，確認邏輯正確
3. **資料備份**：測試前可匯出資料備份
4. **清理資料**：測試完成後可重置系統資料

## 🎯 預期修正效果

1. **餘額計算正確**：所有場景下餘額計算都正確
2. **資料一致性**：儲存值與計算值一致
3. **除錯資訊完整**：可追蹤每個操作的餘額變化
4. **錯誤處理**：異常情況下有適當的警告和修正機制

---

**測試版本**：v2.0
**修正日期**：2024年12月
**測試建議**：完整測試所有場景後再投入使用
