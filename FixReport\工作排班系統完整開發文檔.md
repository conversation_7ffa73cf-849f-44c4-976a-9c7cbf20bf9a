# 工作排班系統完整開發文檔

## 📋 專案概述

### **專案名稱**：工作排班系統
### **主要檔案**：`schedule-app-simple.html`
### **開發者**：wenbin
### **開發語言**：HTML + JavaScript + Tailwind CSS
### **當前狀態**：核心功能已實現，需修正額外班餘額問題並完善系統

## 🏗️ 系統架構

### **用戶角色與權限**
1. **管理員 (Admin)**
   - 系統管理、用戶管理
   - 設定員工週班別、月休天數
   - 管理額外班和事病假額度
   - 查看所有統計報表

2. **主管 (Supervisor)**
   - 審核員工排班申請
   - 為員工加註額外班
   - 查看部門排班狀況
   - 批准/拒絕請假申請

3. **員工 (Employee)**
   - 提交月度排班申請
   - 查看個人額外班餘額
   - 申請補休、事病假
   - 查看申請狀態

### **核心功能模組**
1. **用戶認證系統**：登入/登出、角色權限控制
2. **排班申請系統**：月曆式排班申請與編輯
3. **審核系統**：主管審核排班申請
4. **額外班管理**：主管設定、員工使用補休
5. **事病假管理**：年度額度管理與使用
6. **通知系統**：申請狀態通知
7. **統計報表**：工時統計、排班報表

## 📊 資料結構

### **系統資料 (systemData)**
```javascript
systemData = {
    users: [
        {
            id: "1",
            username: "admin",
            password: "admin123",
            name: "系統管理員",
            role: "admin",
            location: "ciguang",
            monthlyLeaveDays: 8,
            weeklySchedule: {...},
            overtimeBalance: 0,
            annualSickLeaveQuota: 14,
            usedSickLeaveDays: 0
        }
    ],
    scheduleRequests: [
        {
            id: "SR...",
            employeeId: "3",
            employeeName: "員工姓名",
            month: "2024-12",
            scheduleData: {...},
            status: "pending|approved|rejected",
            hasCompensatoryLeave: true,
            compensatoryDaysCount: 2,
            hasSickLeave: false,
            sickLeaveDaysCount: 0,
            originalCompensatoryDays: 0, // 重新編輯時的原始值
            submitDate: "2024-12-...",
            approver: "主管姓名",
            approveDate: "2024-12-...",
            approvalComment: "審核意見"
        }
    ],
    overtimeRecords: [
        {
            id: "OT...",
            employeeId: "3",
            employeeName: "員工姓名",
            date: "2024-12-15",
            type: "half|full",
            reason: "加班原因",
            addedBy: "主管姓名",
            addedDate: "2024-12-..."
        }
    ],
    notifications: [...]
}
```

### **排班資料結構 (scheduleData)**
```javascript
scheduleData = {
    1: { type: "early", location: "ciguang" },
    2: { type: "mid", location: "ruiguang" },
    3: { type: "custom", startTime: "10:00", duration: "full", location: "ciguang" },
    4: { type: "off" },
    5: { type: "compensatory" },
    6: { type: "sick" },
    7: { type: "leave" }
}
```

## 🔧 核心功能實現狀態

### **✅ 已完成功能**
1. **用戶認證系統**
   - 登入/登出功能
   - 角色權限控制
   - 用戶資料管理

2. **排班申請系統**
   - 月曆式排班介面
   - 班別選擇（早班、中班、自訂班別）
   - 工作地點選擇（慈光、瑞光、臨時調差）
   - 休假類型選擇（正常休假、補休、事病假、請假）

3. **審核系統**
   - 主管審核介面
   - 批准/拒絕申請
   - 批量審核功能
   - 審核意見記錄

4. **額外班管理**
   - 主管為員工加註額外班
   - 員工查看額外班餘額
   - 補休功能（消耗額外班）

5. **事病假管理**
   - 年度額度設定
   - 使用記錄追蹤
   - 餘額顯示

6. **通知系統**
   - 申請狀態通知
   - 未讀通知提醒

### **⚠️ 需要修正的問題**
1. **額外班餘額計算錯誤**
   - 主管批准後餘額不正確
   - 重新編輯時餘額顯示錯誤
   - 可能存在重複扣除問題

2. **資料一致性問題**
   - 不同頁面顯示的餘額可能不一致
   - 需要統一餘額計算邏輯

### **🚧 待開發功能**
1. **用戶管理系統**
   - 新增/編輯/刪除用戶
   - 批量匯入用戶
   - 用戶權限管理

2. **系統設定**
   - 班別時間設定
   - 地點管理
   - 系統參數配置

3. **統計報表**
   - 月度工時統計
   - 年度排班報表
   - 請假統計報表
   - 資料匯出功能

4. **進階功能**
   - 排班衝突檢測
   - 人力需求分析
   - 自動排班建議
   - 行動裝置適配

## 🔍 關鍵程式碼區段

### **重要函數位置**
- **用戶認證**：`login()`, `logout()` - 第200-300行
- **排班申請**：`submitScheduleRequest()` - 第2072-2195行
- **主管審核**：`processApproval()` - 第2719-2770行
- **額外班管理**：`addOvertimeShift()` - 第3800-3900行
- **餘額計算**：`recalculateEmployeeOvertimeBalance()` - 第3972-4001行
- **資料儲存**：`saveSystemData()`, `loadSystemData()` - 第100-200行

### **關鍵介面元素**
- **主選單**：角色權限控制的功能選單
- **月曆介面**：排班申請的核心介面
- **審核介面**：主管處理申請的介面
- **額外班管理**：主管設定額外班的介面

## 🧪 測試場景

### **基本功能測試**
1. **用戶登入測試**
   - 管理員：admin/admin123
   - 主管：supervisor/super123
   - 員工：employee1/emp123

2. **排班申請測試**
   - 新申請提交
   - 重新編輯已批准申請
   - 包含補休的申請
   - 包含事病假的申請

3. **審核流程測試**
   - 單個申請審核
   - 批量申請審核
   - 拒絕申請處理

4. **額外班測試**
   - 主管加註額外班
   - 員工使用補休
   - 餘額計算驗證

### **除錯指令**
```javascript
// 檢查系統狀態
debugSystemData();

// 檢查特定員工資料
systemData.users.find(u => u.id === '3');

// 檢查額外班記錄
systemData.overtimeRecords.filter(r => r.employeeId === '3');

// 手動重新計算餘額
recalculateEmployeeOvertimeBalance('3');

// 檢查申請記錄
systemData.scheduleRequests.filter(r => r.employeeId === '3');
```

## 🎯 開發優先順序

### **第一優先：修正核心問題**
1. 修正額外班餘額計算錯誤
2. 確保資料一致性
3. 完善錯誤處理

### **第二優先：完善現有功能**
1. 優化使用者介面
2. 增強資料驗證
3. 改善效能

### **第三優先：新增功能**
1. 用戶管理系統
2. 統計報表功能
3. 系統設定介面

### **第四優先：進階功能**
1. 排班衝突檢測
2. 自動排班建議
3. 行動裝置適配

## 💡 開發建議與最佳實踐

### **程式碼組織**
- 保持函數功能單一
- 使用清楚的命名規則
- 添加適當的註解
- 定期重構優化

### **資料管理**
- 確保資料一致性
- 實施資料驗證
- 定期備份重要資料
- 考慮資料庫遷移

### **使用者體驗**
- 提供清楚的操作回饋
- 實施錯誤處理機制
- 優化載入速度
- 確保介面響應式設計

### **安全性考量**
- 實施適當的權限控制
- 驗證用戶輸入
- 保護敏感資料
- 定期安全檢查

## 📚 相關文檔

### **已建立文檔**
- `工作排班系統開發備忘錄.md` - 完整需求規格
- `專案開發記錄_額外班餘額問題.md` - 問題追蹤記錄
- `額外班邏輯問題分析.md` - 深度問題分析

### **建議建立文檔**
- API 文檔
- 使用者手冊
- 部署指南
- 維護手冊

## 🚀 部署與維護

### **部署需求**
- 現代瀏覽器支援
- 本地檔案系統存取
- 無需伺服器環境

### **維護重點**
- 定期資料備份
- 功能測試驗證
- 效能監控
- 用戶回饋收集

---

**文檔建立時間**：2024年12月
**適用對話**：新對話完整接續開發
**下次更新**：問題修正後更新狀態
