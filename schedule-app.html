<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班管理系統 - 完整版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <!-- Google Sheets API -->
    <script src="https://apis.google.com/js/api.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 導航欄 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="calendar" class="h-8 w-8 text-indigo-600 mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">排班管理系統</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="user-name" class="text-sm text-gray-600">未登入</span>
                    <button id="logout-btn" onclick="logout()" class="hidden text-sm text-gray-600 hover:text-gray-900">
                        <i data-lucide="log-out" class="h-4 w-4 mr-1 inline"></i>
                        登出
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要內容區域 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 登入頁面 -->
        <div id="login-page" class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4">
            <div class="max-w-md w-full space-y-8">
                <div class="text-center">
                    <i data-lucide="calendar" class="h-12 w-12 text-indigo-600 mx-auto mb-4"></i>
                    <h2 class="text-3xl font-extrabold text-gray-900">登入您的帳戶</h2>
                    <p class="mt-2 text-sm text-gray-600">開始使用排班管理系統</p>
                </div>

                <!-- Google Sheets 設定 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <h3 class="text-sm font-medium text-yellow-800 mb-2">Google Sheets 設定：</h3>
                    <div class="space-y-2">
                        <input
                            type="text"
                            id="sheets-id"
                            placeholder="Google Sheets ID (從網址複製)"
                            class="w-full px-3 py-2 border border-yellow-300 rounded-md text-sm"
                            value="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
                        />
                        <input
                            type="text"
                            id="api-key"
                            placeholder="Google API Key"
                            class="w-full px-3 py-2 border border-yellow-300 rounded-md text-sm"
                        />
                        <button onclick="connectGoogleSheets()" class="w-full bg-yellow-600 text-white px-4 py-2 rounded-md text-sm hover:bg-yellow-700">
                            <span id="connect-btn-text">連接 Google Sheets</span>
                        </button>
                        <div class="text-xs text-yellow-700">
                            <p>📋 <a href="#" onclick="showSetupGuide()" class="underline">查看完整設定指南</a></p>
                            <p>🧪 預設使用 Google 範例試算表進行測試</p>
                            <p>🔧 <a href="#" onclick="runDiagnostics()" class="underline">執行連接診斷</a></p>
                        </div>
                    </div>
                </div>

                <!-- 測試帳號 -->
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-2">測試帳號：</h3>
                    <div class="text-xs text-blue-700 space-y-1">
                        <div>管理員: <EMAIL> / admin123</div>
                        <div>主管: <EMAIL> / super123</div>
                        <div>員工: <EMAIL> / emp123</div>
                    </div>
                </div>

                <form onsubmit="handleLogin(event)" class="space-y-6">
                    <div id="login-error" class="hidden bg-red-50 border border-red-200 rounded-md p-4">
                        <span class="text-sm text-red-700" id="login-error-text"></span>
                    </div>

                    <div class="space-y-4">
                        <div class="relative">
                            <i data-lucide="mail" class="absolute left-3 top-3 h-5 w-5 text-gray-400"></i>
                            <input
                                id="email"
                                type="email"
                                required
                                class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="電子郵件地址"
                            />
                        </div>
                        <div class="relative">
                            <i data-lucide="lock" class="absolute left-3 top-3 h-5 w-5 text-gray-400"></i>
                            <input
                                id="password"
                                type="password"
                                required
                                class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="密碼"
                            />
                            <button type="button" onclick="togglePassword()" class="absolute right-3 top-3 text-gray-400">
                                <i data-lucide="eye" id="eye-icon"></i>
                            </button>
                        </div>
                    </div>

                    <button
                        type="submit"
                        id="login-button"
                        class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                        登入
                    </button>
                </form>
            </div>
        </div>

        <!-- 儀表板頁面 -->
        <div id="dashboard-page" class="hidden">
            <div class="px-4 py-6 sm:px-0">
                <!-- 歡迎訊息 -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">歡迎回來，<span id="dashboard-user-name"></span>！</h2>
                    <p class="text-gray-600">您的角色：<span id="dashboard-user-role"></span> | 地點：<span id="dashboard-user-location"></span></p>
                </div>

                <!-- Google Sheets 狀態 -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Google Sheets 連接狀態</h3>
                            <p id="sheets-status" class="text-sm text-gray-600">未連接</p>
                        </div>
                        <div id="sheets-indicator" class="h-3 w-3 bg-red-400 rounded-full"></div>
                    </div>
                </div>

                <!-- 功能區域 -->
                <div id="employee-functions" class="hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">員工功能</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <button onclick="showScheduleForm()" class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow border-2 border-dashed border-gray-300 hover:border-indigo-400">
                            <i data-lucide="plus" class="h-8 w-8 text-indigo-600 mx-auto mb-2"></i>
                            <h4 class="text-lg font-medium text-gray-900 text-center">新增排班申請</h4>
                            <p class="text-sm text-gray-600 text-center mt-1">提交下月排班需求</p>
                        </button>
                        <button onclick="showShiftChangeForm()" class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                            <i data-lucide="refresh-cw" class="h-8 w-8 text-blue-600 mx-auto mb-2"></i>
                            <h4 class="text-lg font-medium text-gray-900 text-center">調班申請</h4>
                            <p class="text-sm text-gray-600 text-center mt-1">申請臨時調班</p>
                        </button>
                        <button onclick="showReports()" class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
                            <i data-lucide="file-text" class="h-8 w-8 text-green-600 mx-auto mb-2"></i>
                            <h4 class="text-lg font-medium text-gray-900 text-center">工時報表</h4>
                            <p class="text-sm text-gray-600 text-center mt-1">查看工時統計</p>
                        </button>
                    </div>
                </div>

                <div id="supervisor-functions" class="hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">主管功能</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-blue-600">8</div>
                            <div class="text-sm text-blue-700">總員工數</div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-yellow-600">3</div>
                            <div class="text-sm text-yellow-700">待審核申請</div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-green-600">12</div>
                            <div class="text-sm text-green-700">本月已核准</div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-purple-600">慈光2/瑞光1</div>
                            <div class="text-sm text-purple-700">今日人力</div>
                        </div>
                    </div>
                </div>

                <div id="admin-functions" class="hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">系統管理功能</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-blue-600">15</div>
                            <div class="text-sm text-blue-700">總用戶數</div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-yellow-600">3</div>
                            <div class="text-sm text-yellow-700">待審核註冊</div>
                        </div>
                        <div class="bg-white p-6 rounded-lg shadow text-center">
                            <div class="text-2xl font-bold text-green-600">正常</div>
                            <div class="text-sm text-green-700">系統狀態</div>
                        </div>
                    </div>
                </div>

                <!-- 資料顯示區域 -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">系統資料</h3>
                    <div id="data-display" class="text-sm text-gray-600">
                        <p>連接 Google Sheets 後，這裡將顯示即時資料</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentUser = null;
        let sheetsAPI = null;
        let sheetsId = null;
        let apiKey = null;

        // 模擬用戶資料
        const mockUsers = [
            {
                id: '1',
                email: '<EMAIL>',
                password: 'admin123',
                name: '系統管理員',
                role: 'admin',
                location: 'ciguang',
                approved: true
            },
            {
                id: '2',
                email: '<EMAIL>',
                password: 'super123',
                name: '主管',
                role: 'supervisor',
                location: 'ciguang',
                approved: true
            },
            {
                id: '3',
                email: '<EMAIL>',
                password: 'emp123',
                name: '員工',
                role: 'employee',
                location: 'ruiguang',
                approved: true
            }
        ];

        // 初始化
        function init() {
            lucide.createIcons();

            // 檢查是否有儲存的登入狀態
            const savedUser = localStorage.getItem('currentUser');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                showDashboard();
            }
        }

        // 密碼顯示切換
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.setAttribute('data-lucide', 'eye-off');
            } else {
                passwordInput.type = 'password';
                eyeIcon.setAttribute('data-lucide', 'eye');
            }
            lucide.createIcons();
        }

        // 登入處理
        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const button = document.getElementById('login-button');
            const errorDiv = document.getElementById('login-error');
            const errorText = document.getElementById('login-error-text');

            button.textContent = '登入中...';
            button.disabled = true;
            errorDiv.classList.add('hidden');

            setTimeout(() => {
                const user = mockUsers.find(u => u.email === email && u.password === password);

                if (user) {
                    if (!user.approved) {
                        errorText.textContent = '您的帳戶尚未通過審核，請聯繫管理員。';
                        errorDiv.classList.remove('hidden');
                    } else {
                        currentUser = user;
                        localStorage.setItem('currentUser', JSON.stringify(user));
                        showDashboard();
                    }
                } else {
                    errorText.textContent = '電子郵件或密碼錯誤';
                    errorDiv.classList.remove('hidden');
                }

                button.textContent = '登入';
                button.disabled = false;
            }, 1000);
        }

        // 顯示儀表板
        function showDashboard() {
            document.getElementById('login-page').classList.add('hidden');
            document.getElementById('dashboard-page').classList.remove('hidden');

            // 更新用戶資訊
            document.getElementById('user-name').textContent = currentUser.name;
            document.getElementById('logout-btn').classList.remove('hidden');
            document.getElementById('dashboard-user-name').textContent = currentUser.name;
            document.getElementById('dashboard-user-role').textContent = getRoleText(currentUser.role);
            document.getElementById('dashboard-user-location').textContent = currentUser.location === 'ciguang' ? '慈光' : '瑞光';

            // 顯示對應角色的功能
            document.getElementById('employee-functions').classList.add('hidden');
            document.getElementById('supervisor-functions').classList.add('hidden');
            document.getElementById('admin-functions').classList.add('hidden');

            document.getElementById(`${currentUser.role}-functions`).classList.remove('hidden');
        }

        // 登出
        function logout() {
            currentUser = null;
            localStorage.removeItem('currentUser');
            document.getElementById('login-page').classList.remove('hidden');
            document.getElementById('dashboard-page').classList.add('hidden');
            document.getElementById('user-name').textContent = '未登入';
            document.getElementById('logout-btn').classList.add('hidden');
        }

        // 角色文字轉換
        function getRoleText(role) {
            switch (role) {
                case 'admin': return '系統管理員';
                case 'supervisor': return '主管';
                case 'employee': return '員工';
                default: return '未知';
            }
        }

        // Google Sheets 連接
        function connectGoogleSheets() {
            sheetsId = document.getElementById('sheets-id').value;
            apiKey = document.getElementById('api-key').value;
            const connectBtn = document.getElementById('connect-btn-text');

            if (!sheetsId) {
                alert('請輸入 Google Sheets ID');
                return;
            }

            if (!apiKey) {
                alert('請輸入 Google API Key\n\n設定步驟：\n1. 前往 Google Cloud Console\n2. 啟用 Google Sheets API\n3. 創建 API 金鑰\n4. 將金鑰貼到此處');
                return;
            }

            connectBtn.textContent = '連接中...';
            updateSheetsStatus(false, '正在載入 Google API...');

            // 檢查 Google API 是否已載入
            if (typeof gapi === 'undefined') {
                console.error('Google API 未載入');
                updateSheetsStatus(false, 'Google API 載入失敗');
                connectBtn.textContent = '連接 Google Sheets';
                alert('Google API 載入失敗\n\n可能原因：\n• 網路連接問題\n• 瀏覽器阻擋了外部腳本\n• 請重新整理頁面再試');
                return;
            }

            // 設定超時處理
            const timeout = setTimeout(() => {
                console.error('Google API 載入超時');
                updateSheetsStatus(false, 'API 載入超時');
                connectBtn.textContent = '連接 Google Sheets';
                alert('Google API 載入超時\n\n請檢查：\n• 網路連接是否正常\n• 是否有防火牆阻擋\n• 重新整理頁面再試');
            }, 10000); // 10秒超時

            // 初始化 Google Sheets API
            try {
                gapi.load('client', () => {
                    clearTimeout(timeout);
                    initializeGapi();
                });
            } catch (error) {
                clearTimeout(timeout);
                console.error('gapi.load 失敗:', error);
                updateSheetsStatus(false, 'API 載入失敗');
                connectBtn.textContent = '連接 Google Sheets';
                alert('Google API 載入失敗：' + error.message);
            }
        }

        function initializeGapi() {
            updateSheetsStatus(false, '正在初始化 API...');

            const initTimeout = setTimeout(() => {
                console.error('API 初始化超時');
                updateSheetsStatus(false, 'API 初始化超時');
                document.getElementById('connect-btn-text').textContent = '連接 Google Sheets';
                alert('API 初始化超時\n\n請重新整理頁面再試');
            }, 15000); // 15秒超時

            gapi.client.init({
                apiKey: apiKey,
                discoveryDocs: ['https://sheets.googleapis.com/$discovery/rest?version=v4']
            }).then(() => {
                clearTimeout(initTimeout);
                console.log('Google Sheets API 初始化成功');
                updateSheetsStatus(true, 'API 連接成功');

                // 測試 API 連接
                testApiConnection();

            }).catch(error => {
                clearTimeout(initTimeout);
                console.error('Google Sheets API 初始化失敗:', error);
                updateSheetsStatus(false, 'API 初始化失敗');
                document.getElementById('connect-btn-text').textContent = '連接 Google Sheets';

                let errorMsg = 'Google Sheets API 初始化失敗\n\n';

                if (error.result && error.result.error) {
                    const apiError = error.result.error;
                    if (apiError.code === 400) {
                        errorMsg += '錯誤：API Key 無效或格式錯誤\n';
                        errorMsg += '請檢查：\n• API Key 是否正確複製\n• API Key 是否已啟用 Google Sheets API';
                    } else if (apiError.code === 403) {
                        errorMsg += '錯誤：權限不足\n';
                        errorMsg += '請檢查：\n• API Key 權限設定\n• Google Sheets 共享設定';
                    } else {
                        errorMsg += `錯誤代碼：${apiError.code}\n`;
                        errorMsg += `錯誤訊息：${apiError.message}`;
                    }
                } else if (error.message) {
                    errorMsg += `錯誤：${error.message}`;
                } else {
                    errorMsg += '未知錯誤，請檢查：\n• 網路連接\n• API Key 設定\n• 瀏覽器控制台錯誤訊息';
                }

                alert(errorMsg);
            });
        }

        function testApiConnection() {
            updateSheetsStatus(true, '測試 API 連接...');

            // 先測試一個簡單的 API 呼叫
            gapi.client.sheets.spreadsheets.get({
                spreadsheetId: sheetsId
            }).then(response => {
                console.log('API 連接測試成功:', response);
                updateSheetsStatus(true, 'API 連接成功');
                document.getElementById('connect-btn-text').textContent = '重新連接';
                loadSheetsData();
            }).catch(error => {
                console.error('API 連接測試失敗:', error);

                let errorMsg = 'Google Sheets 存取失敗\n\n';

                if (error.result && error.result.error) {
                    const apiError = error.result.error;
                    if (apiError.code === 404) {
                        errorMsg += '錯誤：找不到指定的 Google Sheets\n';
                        errorMsg += '請檢查：\n• Sheets ID 是否正確\n• Sheets 是否存在';
                    } else if (apiError.code === 403) {
                        errorMsg += '錯誤：無權限存取此 Google Sheets\n';
                        errorMsg += '請檢查：\n• Sheets 共享設定為「知道連結的任何人」\n• 權限設為「檢視者」或以上';
                    } else {
                        errorMsg += `錯誤代碼：${apiError.code}\n`;
                        errorMsg += `錯誤訊息：${apiError.message}`;
                    }
                } else {
                    errorMsg += '請檢查 Sheets ID 和權限設定';
                }

                updateSheetsStatus(false, 'Sheets 存取失敗');
                document.getElementById('connect-btn-text').textContent = '連接 Google Sheets';
                alert(errorMsg);
            });
        }

        function updateSheetsStatus(connected, message = '') {
            const statusText = document.getElementById('sheets-status');
            const indicator = document.getElementById('sheets-indicator');

            if (connected) {
                statusText.textContent = message || '已連接';
                indicator.className = 'h-3 w-3 bg-green-400 rounded-full';
            } else {
                statusText.textContent = message || '未連接';
                indicator.className = 'h-3 w-3 bg-red-400 rounded-full';
            }
        }

        function loadSheetsData() {
            if (!sheetsId) return;

            updateSheetsStatus(true, '載入資料中...');

            // 嘗試讀取多個工作表
            const ranges = [
                'Class Data!A1:F30',  // Google 範例試算表的預設工作表
                'Sheet1!A1:Z100',     // 備用範圍
                'A1:Z100'             // 預設範圍
            ];

            // 嘗試第一個範圍
            gapi.client.sheets.spreadsheets.values.get({
                spreadsheetId: sheetsId,
                range: ranges[0]
            }).then(response => {
                const values = response.result.values;
                if (values && values.length > 0) {
                    displayData(values, 'Class Data');
                    updateSheetsStatus(true, '資料載入成功');
                } else {
                    // 嘗試其他範圍
                    tryAlternativeRanges(ranges.slice(1));
                }
            }).catch(error => {
                console.error('讀取主要範圍失敗:', error);
                // 嘗試其他範圍
                tryAlternativeRanges(ranges.slice(1));
            });
        }

        function tryAlternativeRanges(ranges) {
            if (ranges.length === 0) {
                updateSheetsStatus(false, '無法讀取資料');
                document.getElementById('data-display').innerHTML = `
                    <div class="text-red-600">
                        <p class="font-medium">讀取資料失敗</p>
                        <p class="text-sm mt-2">可能原因：</p>
                        <ul class="text-sm mt-1 list-disc list-inside">
                            <li>Google Sheets 沒有資料</li>
                            <li>工作表名稱不正確</li>
                            <li>權限設定問題</li>
                        </ul>
                        <p class="text-sm mt-2">建議：使用預設的 Google 範例試算表進行測試</p>
                    </div>
                `;
                return;
            }

            const range = ranges[0];
            gapi.client.sheets.spreadsheets.values.get({
                spreadsheetId: sheetsId,
                range: range
            }).then(response => {
                const values = response.result.values;
                if (values && values.length > 0) {
                    displayData(values, range);
                    updateSheetsStatus(true, '資料載入成功');
                } else {
                    tryAlternativeRanges(ranges.slice(1));
                }
            }).catch(error => {
                console.error(`讀取範圍 ${range} 失敗:`, error);
                tryAlternativeRanges(ranges.slice(1));
            });
        }

        function displayData(data, sheetName = '') {
            const displayDiv = document.getElementById('data-display');

            if (!data || data.length === 0) {
                displayDiv.innerHTML = '<p>Google Sheets 中沒有資料</p>';
                return;
            }

            let html = `
                <div class="mb-4">
                    <h4 class="text-lg font-medium text-gray-900">Google Sheets 資料</h4>
                    ${sheetName ? `<p class="text-sm text-gray-600">工作表：${sheetName}</p>` : ''}
                    <p class="text-sm text-gray-600">共 ${data.length - 1} 筆資料</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
            `;

            // 表頭
            if (data[0]) {
                html += '<tr>';
                data[0].forEach(header => {
                    html += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${header || '未命名'}</th>`;
                });
                html += '</tr>';
            }

            html += '</thead><tbody class="bg-white divide-y divide-gray-200">';

            // 資料行（最多顯示 10 筆）
            const maxRows = Math.min(data.length, 11);
            for (let i = 1; i < maxRows; i++) {
                html += '<tr class="hover:bg-gray-50">';
                const maxCols = Math.max(data[0] ? data[0].length : 0, data[i] ? data[i].length : 0);
                for (let j = 0; j < maxCols; j++) {
                    const cell = data[i] && data[i][j] ? data[i][j] : '';
                    html += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cell}</td>`;
                }
                html += '</tr>';
            }

            html += '</tbody></table></div>';

            if (data.length > 11) {
                html += `
                    <div class="mt-4 p-3 bg-blue-50 rounded-md">
                        <p class="text-sm text-blue-700">
                            <i data-lucide="info" class="h-4 w-4 inline mr-1"></i>
                            顯示前 10 筆資料，共 ${data.length - 1} 筆
                        </p>
                    </div>
                `;
            }

            displayDiv.innerHTML = html;
            lucide.createIcons();
        }

        // 診斷功能
        function runDiagnostics() {
            const diagnosticResults = [];

            // 檢查 1: Google API 載入
            if (typeof gapi === 'undefined') {
                diagnosticResults.push({
                    test: 'Google API 載入',
                    status: 'failed',
                    message: 'Google API 未載入',
                    solution: '請檢查網路連接，重新整理頁面'
                });
            } else {
                diagnosticResults.push({
                    test: 'Google API 載入',
                    status: 'passed',
                    message: 'Google API 已成功載入'
                });
            }

            // 檢查 2: API Key 格式
            const apiKeyInput = document.getElementById('api-key').value;
            if (!apiKeyInput) {
                diagnosticResults.push({
                    test: 'API Key 檢查',
                    status: 'failed',
                    message: 'API Key 未輸入',
                    solution: '請輸入有效的 Google API Key'
                });
            } else if (apiKeyInput.length < 30) {
                diagnosticResults.push({
                    test: 'API Key 檢查',
                    status: 'warning',
                    message: 'API Key 長度可能不正確',
                    solution: '請確認 API Key 完整複製'
                });
            } else {
                diagnosticResults.push({
                    test: 'API Key 檢查',
                    status: 'passed',
                    message: 'API Key 格式看起來正確'
                });
            }

            // 檢查 3: Sheets ID 格式
            const sheetsIdInput = document.getElementById('sheets-id').value;
            if (!sheetsIdInput) {
                diagnosticResults.push({
                    test: 'Sheets ID 檢查',
                    status: 'failed',
                    message: 'Sheets ID 未輸入',
                    solution: '請輸入 Google Sheets ID'
                });
            } else if (!/^[a-zA-Z0-9-_]{44}$/.test(sheetsIdInput)) {
                diagnosticResults.push({
                    test: 'Sheets ID 檢查',
                    status: 'warning',
                    message: 'Sheets ID 格式可能不正確',
                    solution: '請確認從 Google Sheets 網址正確複製 ID'
                });
            } else {
                diagnosticResults.push({
                    test: 'Sheets ID 檢查',
                    status: 'passed',
                    message: 'Sheets ID 格式正確'
                });
            }

            // 檢查 4: 瀏覽器支援
            if (!window.fetch) {
                diagnosticResults.push({
                    test: '瀏覽器支援',
                    status: 'failed',
                    message: '瀏覽器不支援現代 API',
                    solution: '請使用較新版本的瀏覽器'
                });
            } else {
                diagnosticResults.push({
                    test: '瀏覽器支援',
                    status: 'passed',
                    message: '瀏覽器支援所需功能'
                });
            }

            showDiagnosticResults(diagnosticResults);
        }

        function showDiagnosticResults(results) {
            let html = '<div class="space-y-4">';

            results.forEach(result => {
                let statusColor, statusIcon;
                switch (result.status) {
                    case 'passed':
                        statusColor = 'text-green-600';
                        statusIcon = 'check-circle';
                        break;
                    case 'warning':
                        statusColor = 'text-yellow-600';
                        statusIcon = 'alert-triangle';
                        break;
                    case 'failed':
                        statusColor = 'text-red-600';
                        statusIcon = 'x-circle';
                        break;
                }

                html += `
                    <div class="border rounded-lg p-4">
                        <div class="flex items-start">
                            <i data-lucide="${statusIcon}" class="h-5 w-5 ${statusColor} mr-3 mt-0.5"></i>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">${result.test}</h4>
                                <p class="text-sm text-gray-600 mt-1">${result.message}</p>
                                ${result.solution ? `<p class="text-sm text-blue-600 mt-2">💡 ${result.solution}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            showModal('連接診斷結果', html);
        }

        // 設定指南
        function showSetupGuide() {
            const guideContent = `
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">Google Sheets API 設定指南</h3>
                    </div>

                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-medium text-gray-900">步驟 1: 創建 Google Cloud 項目</h4>
                            <ol class="mt-2 text-sm text-gray-600 list-decimal list-inside space-y-1">
                                <li>前往 <a href="https://console.cloud.google.com/" target="_blank" class="text-blue-600 underline">Google Cloud Console</a></li>
                                <li>點擊「新增項目」</li>
                                <li>輸入項目名稱：workdays-scheduler</li>
                                <li>點擊「建立」</li>
                            </ol>
                        </div>

                        <div class="border-l-4 border-green-500 pl-4">
                            <h4 class="font-medium text-gray-900">步驟 2: 啟用 Google Sheets API</h4>
                            <ol class="mt-2 text-sm text-gray-600 list-decimal list-inside space-y-1">
                                <li>在左側選單選擇「API 和服務」→「程式庫」</li>
                                <li>搜尋「Google Sheets API」</li>
                                <li>點擊「Google Sheets API」</li>
                                <li>點擊「啟用」</li>
                            </ol>
                        </div>

                        <div class="border-l-4 border-yellow-500 pl-4">
                            <h4 class="font-medium text-gray-900">步驟 3: 創建 API 金鑰</h4>
                            <ol class="mt-2 text-sm text-gray-600 list-decimal list-inside space-y-1">
                                <li>前往「API 和服務」→「憑證」</li>
                                <li>點擊「+ 建立憑證」→「API 金鑰」</li>
                                <li>複製生成的 API 金鑰</li>
                                <li>建議：限制金鑰只能使用 Google Sheets API</li>
                            </ol>
                        </div>

                        <div class="border-l-4 border-purple-500 pl-4">
                            <h4 class="font-medium text-gray-900">步驟 4: 準備 Google Sheets</h4>
                            <ol class="mt-2 text-sm text-gray-600 list-decimal list-inside space-y-1">
                                <li>創建新的 Google Sheets 或使用現有的</li>
                                <li>點擊「共用」→「知道連結的任何人」→「檢視者」</li>
                                <li>從網址複製 Sheets ID（/d/ 和 /edit 之間的部分）</li>
                            </ol>
                        </div>

                        <div class="bg-blue-50 p-4 rounded-md">
                            <h4 class="font-medium text-blue-900">🧪 快速測試</h4>
                            <p class="text-sm text-blue-700 mt-1">
                                可以使用預設的 Google 範例試算表進行測試：<br>
                                <code class="bg-blue-100 px-2 py-1 rounded text-xs">1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms</code>
                            </p>
                        </div>
                    </div>
                </div>
            `;

            showModal('Google Sheets API 設定指南', guideContent);
        }

        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                                <i data-lucide="x" class="h-6 w-6"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-700">
                            ${content}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            lucide.createIcons();

            // 點擊外部關閉
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 功能按鈕處理
        function showScheduleForm() {
            alert('排班申請功能開發中...\n\n下一步將開發：\n• 排班申請表單\n• 班別選擇\n• 提交到 Google Sheets');
        }

        function showShiftChangeForm() {
            alert('調班申請功能開發中...\n\n下一步將開發：\n• 調班申請表單\n• 審核流程\n• 通知系統');
        }

        function showReports() {
            alert('報表功能開發中...\n\n下一步將開發：\n• 工時統計\n• 排班報表\n• 匯出功能');
        }

        // 頁面載入時初始化
        window.onload = init;
    </script>
</body>
</html>
