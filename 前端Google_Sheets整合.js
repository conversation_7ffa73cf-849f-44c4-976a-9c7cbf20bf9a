/**
 * 工作排班管理系統 - Google Sheets 前端整合
 * 版本：v1.0.0
 * 作者：Augment Agent
 * 日期：2024-12
 */

// ==================== 配置設定 ====================

// Google Apps Script Web App URL（需要替換為實際的 URL）
const GOOGLE_APPS_SCRIPT_URL = 'YOUR_GOOGLE_APPS_SCRIPT_URL_HERE';

// API 配置
const API_CONFIG = {
    timeout: 30000, // 30秒超時
    retryAttempts: 3,
    retryDelay: 1000 // 1秒重試延遲
};

// 本地快取配置
const CACHE_CONFIG = {
    enabled: true,
    duration: 5 * 60 * 1000, // 5分鐘快取
    keys: {
        users: 'cache_users',
        scheduleRequests: 'cache_schedule_requests',
        systemSettings: 'cache_system_settings'
    }
};

// ==================== 核心 API 類別 ====================

class GoogleSheetsAPI {
    constructor() {
        this.authToken = null;
        this.currentUser = null;
        this.isOnline = navigator.onLine;
        this.pendingRequests = [];
        
        // 監聽網路狀態
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processPendingRequests();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    /**
     * 設定認證資訊
     */
    setAuth(user, token) {
        this.currentUser = user;
        this.authToken = token;
        
        // 儲存到 localStorage 以便頁面重新載入時恢復
        localStorage.setItem('currentUser', JSON.stringify(user));
        localStorage.setItem('authToken', token);
    }

    /**
     * 從 localStorage 恢復認證資訊
     */
    restoreAuth() {
        try {
            const savedUser = localStorage.getItem('currentUser');
            const savedToken = localStorage.getItem('authToken');
            
            if (savedUser && savedToken) {
                this.currentUser = JSON.parse(savedUser);
                this.authToken = savedToken;
                return true;
            }
        } catch (error) {
            console.error('恢復認證資訊失敗:', error);
        }
        return false;
    }

    /**
     * 清除認證資訊
     */
    clearAuth() {
        this.currentUser = null;
        this.authToken = null;
        localStorage.removeItem('currentUser');
        localStorage.removeItem('authToken');
    }

    /**
     * 發送 API 請求
     */
    async makeRequest(action, data = null, options = {}) {
        const requestData = {
            action: action,
            data: data,
            auth: {
                userId: this.currentUser?.id,
                token: this.authToken
            }
        };

        // 如果離線，將請求加入待處理佇列
        if (!this.isOnline && !options.skipOfflineQueue) {
            this.pendingRequests.push({ action, data, options });
            throw new Error('目前離線，請求已加入待處理佇列');
        }

        try {
            const response = await this.fetchWithRetry(GOOGLE_APPS_SCRIPT_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '請求失敗');
            }

            // 更新快取
            if (CACHE_CONFIG.enabled && this.shouldCache(action)) {
                this.updateCache(action, result.data);
            }

            return result.data;

        } catch (error) {
            console.error(`API 請求失敗 (${action}):`, error);
            
            // 嘗試從快取獲取資料
            if (CACHE_CONFIG.enabled && this.shouldCache(action)) {
                const cachedData = this.getFromCache(action);
                if (cachedData) {
                    console.warn(`使用快取資料 (${action})`);
                    return cachedData;
                }
            }
            
            throw error;
        }
    }

    /**
     * 帶重試機制的 fetch
     */
    async fetchWithRetry(url, options, attempt = 1) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response;

        } catch (error) {
            if (attempt < API_CONFIG.retryAttempts) {
                console.warn(`請求失敗，${API_CONFIG.retryDelay}ms 後重試 (${attempt}/${API_CONFIG.retryAttempts}):`, error.message);
                await this.delay(API_CONFIG.retryDelay);
                return this.fetchWithRetry(url, options, attempt + 1);
            }
            throw error;
        }
    }

    /**
     * 延遲函數
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 處理待處理的請求
     */
    async processPendingRequests() {
        if (this.pendingRequests.length === 0) return;

        console.log(`處理 ${this.pendingRequests.length} 個待處理請求`);

        const requests = [...this.pendingRequests];
        this.pendingRequests = [];

        for (const request of requests) {
            try {
                await this.makeRequest(request.action, request.data, { ...request.options, skipOfflineQueue: true });
            } catch (error) {
                console.error('處理待處理請求失敗:', error);
                // 重新加入佇列
                this.pendingRequests.push(request);
            }
        }
    }

    /**
     * 判斷是否應該快取
     */
    shouldCache(action) {
        const cacheableActions = ['getUsers', 'getScheduleRequests', 'getSystemSettings', 'getNotifications'];
        return cacheableActions.includes(action);
    }

    /**
     * 更新快取
     */
    updateCache(action, data) {
        const cacheKey = this.getCacheKey(action);
        if (cacheKey) {
            const cacheData = {
                data: data,
                timestamp: Date.now()
            };
            localStorage.setItem(cacheKey, JSON.stringify(cacheData));
        }
    }

    /**
     * 從快取獲取資料
     */
    getFromCache(action) {
        const cacheKey = this.getCacheKey(action);
        if (!cacheKey) return null;

        try {
            const cached = localStorage.getItem(cacheKey);
            if (!cached) return null;

            const cacheData = JSON.parse(cached);
            const age = Date.now() - cacheData.timestamp;

            if (age > CACHE_CONFIG.duration) {
                localStorage.removeItem(cacheKey);
                return null;
            }

            return cacheData.data;
        } catch (error) {
            console.error('讀取快取失敗:', error);
            return null;
        }
    }

    /**
     * 獲取快取鍵值
     */
    getCacheKey(action) {
        const keyMap = {
            'getUsers': CACHE_CONFIG.keys.users,
            'getScheduleRequests': CACHE_CONFIG.keys.scheduleRequests,
            'getSystemSettings': CACHE_CONFIG.keys.systemSettings
        };
        return keyMap[action] || null;
    }

    /**
     * 清除所有快取
     */
    clearCache() {
        Object.values(CACHE_CONFIG.keys).forEach(key => {
            localStorage.removeItem(key);
        });
    }
}

// ==================== API 方法封裝 ====================

class WorkScheduleAPI extends GoogleSheetsAPI {
    
    // ==================== 用戶相關 API ====================

    /**
     * 用戶登入
     */
    async login(email, password) {
        try {
            const result = await this.makeRequest('authenticateUser', { email, password });
            this.setAuth(result.user, result.token);
            return result.user;
        } catch (error) {
            throw new Error(`登入失敗: ${error.message}`);
        }
    }

    /**
     * 用戶登出
     */
    logout() {
        this.clearAuth();
        this.clearCache();
    }

    /**
     * 獲取用戶列表
     */
    async getUsers() {
        return await this.makeRequest('getUsers');
    }

    /**
     * 建立新用戶
     */
    async createUser(userData) {
        const result = await this.makeRequest('createUser', userData);
        this.clearCache(); // 清除用戶快取
        return result;
    }

    /**
     * 更新用戶資料
     */
    async updateUser(userData) {
        const result = await this.makeRequest('updateUser', userData);
        this.clearCache(); // 清除用戶快取
        return result;
    }

    /**
     * 刪除用戶
     */
    async deleteUser(userId) {
        const result = await this.makeRequest('deleteUser', { userId });
        this.clearCache(); // 清除用戶快取
        return result;
    }

    // ==================== 排班申請相關 API ====================

    /**
     * 獲取排班申請列表
     */
    async getScheduleRequests(filters = null) {
        return await this.makeRequest('getScheduleRequests', { filters });
    }

    /**
     * 建立排班申請
     */
    async createScheduleRequest(requestData) {
        const result = await this.makeRequest('createScheduleRequest', requestData);
        this.clearCache(); // 清除排班申請快取
        return result;
    }

    /**
     * 更新排班申請
     */
    async updateScheduleRequest(requestData) {
        const result = await this.makeRequest('updateScheduleRequest', requestData);
        this.clearCache(); // 清除排班申請快取
        return result;
    }

    /**
     * 審核排班申請
     */
    async approveScheduleRequest(requestId, status, comments = '') {
        const result = await this.makeRequest('approveScheduleRequest', {
            requestId,
            status,
            comments
        });
        this.clearCache(); // 清除相關快取
        return result;
    }

    // ==================== 額外班記錄相關 API ====================

    /**
     * 獲取額外班記錄
     */
    async getOvertimeRecords(filters = null) {
        return await this.makeRequest('getOvertimeRecords', { filters });
    }

    /**
     * 建立額外班記錄
     */
    async createOvertimeRecord(recordData) {
        const result = await this.makeRequest('createOvertimeRecord', recordData);
        this.clearCache(); // 清除相關快取
        return result;
    }

    // ==================== 通知相關 API ====================

    /**
     * 獲取通知列表
     */
    async getNotifications() {
        return await this.makeRequest('getNotifications');
    }

    /**
     * 標記通知為已讀
     */
    async markNotificationRead(notificationId) {
        return await this.makeRequest('markNotificationRead', { notificationId });
    }

    // ==================== 系統設定相關 API ====================

    /**
     * 獲取系統設定
     */
    async getSystemSettings() {
        return await this.makeRequest('getSystemSettings');
    }

    /**
     * 更新系統設定
     */
    async updateSystemSettings(settings) {
        const result = await this.makeRequest('updateSystemSettings', settings);
        this.clearCache(); // 清除系統設定快取
        return result;
    }

    // ==================== 資料同步相關 API ====================

    /**
     * 同步所有資料
     */
    async syncAllData() {
        return await this.makeRequest('syncAllData');
    }

    /**
     * 從 localStorage 遷移資料
     */
    async migrateFromLocalStorage(localData) {
        return await this.makeRequest('migrateFromLocalStorage', localData);
    }
}

// ==================== 全域 API 實例 ====================

// 建立全域 API 實例
window.workScheduleAPI = new WorkScheduleAPI();

// 頁面載入時嘗試恢復認證
document.addEventListener('DOMContentLoaded', () => {
    if (window.workScheduleAPI.restoreAuth()) {
        console.log('已恢復用戶認證狀態');
    }
});

// ==================== 輔助函數 ====================

/**
 * 顯示載入狀態
 */
function showLoading(message = '載入中...') {
    // 實作載入動畫
    console.log('Loading:', message);
}

/**
 * 隱藏載入狀態
 */
function hideLoading() {
    // 隱藏載入動畫
    console.log('Loading complete');
}

/**
 * 顯示錯誤訊息
 */
function showError(message) {
    alert(`錯誤: ${message}`);
}

/**
 * 顯示成功訊息
 */
function showSuccess(message) {
    alert(`成功: ${message}`);
}

/**
 * 網路狀態指示器
 */
function updateNetworkStatus() {
    const isOnline = navigator.onLine;
    const statusElement = document.getElementById('network-status');
    
    if (statusElement) {
        statusElement.textContent = isOnline ? '線上' : '離線';
        statusElement.className = isOnline ? 'status-online' : 'status-offline';
    }
}

// 監聽網路狀態變化
window.addEventListener('online', updateNetworkStatus);
window.addEventListener('offline', updateNetworkStatus);

// 初始化網路狀態
document.addEventListener('DOMContentLoaded', updateNetworkStatus);
