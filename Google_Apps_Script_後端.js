/**
 * 工作排班管理系統 - Google Apps Script 後端
 * 版本：v1.0.0
 * 作者：Augment Agent
 * 日期：2024-12
 */

// ==================== 配置設定 ====================

// Google Sheets ID（需要替換為實際的 Sheets ID）
// 請將下面的 'YOUR_SPREADSHEET_ID_HERE' 替換為您的 Google Sheets ID
// 例如：const SPREADSHEET_ID = '1ABC123def456GHI789jkl';
const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID_HERE';

// 工作表名稱
const SHEET_NAMES = {
  USERS: 'Users',
  SCHEDULE_REQUESTS: 'ScheduleRequests',
  OVERTIME_RECORDS: 'OvertimeRecords',
  NOTIFICATIONS: 'Notifications',
  EMPLOYEE_SCHEDULES: 'EmployeeSchedules',
  SHIFT_CHANGES: 'ShiftChanges',
  SYSTEM_SETTINGS: 'SystemSettings',
  ACTIVITY_LOGS: 'ActivityLogs'
};

// API 回應格式
const API_RESPONSE = {
  success: (data, message = 'Success') => ({
    success: true,
    message: message,
    data: data,
    timestamp: new Date().toISOString()
  }),
  error: (message, code = 'ERROR') => ({
    success: false,
    error: code,
    message: message,
    timestamp: new Date().toISOString()
  })
};

// ==================== 主要 API 端點 ====================

/**
 * 主要 POST 處理函數
 * 處理所有來自前端的 API 請求
 */
function doPost(e) {
  try {
    // 檢查請求參數是否存在
    if (!e || !e.postData || !e.postData.contents) {
      console.error('Invalid request: missing postData');
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('Invalid request format', 'INVALID_REQUEST')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 解析請求資料
    let requestData;
    try {
      requestData = JSON.parse(e.postData.contents);
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('Invalid JSON format', 'INVALID_JSON')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const { action, data, auth } = requestData;

    // 記錄 API 請求
    logActivity('API_REQUEST', null, 'system', {
      action: action,
      timestamp: new Date().toISOString(),
      userAgent: e.parameter.userAgent || 'Unknown'
    });

    // 路由到對應的處理函數
    switch (action) {
      // 用戶相關
      case 'getUsers':
        return handleGetUsers(auth);
      case 'createUser':
        return handleCreateUser(data, auth);
      case 'updateUser':
        return handleUpdateUser(data, auth);
      case 'deleteUser':
        return handleDeleteUser(data, auth);
      case 'authenticateUser':
        return handleAuthenticateUser(data);

      // 排班申請相關
      case 'getScheduleRequests':
        return handleGetScheduleRequests(data, auth);
      case 'createScheduleRequest':
        return handleCreateScheduleRequest(data, auth);
      case 'updateScheduleRequest':
        return handleUpdateScheduleRequest(data, auth);
      case 'approveScheduleRequest':
        return handleApproveScheduleRequest(data, auth);

      // 額外班記錄相關
      case 'getOvertimeRecords':
        return handleGetOvertimeRecords(data, auth);
      case 'createOvertimeRecord':
        return handleCreateOvertimeRecord(data, auth);
      case 'updateOvertimeRecord':
        return handleUpdateOvertimeRecord(data, auth);

      // 通知相關
      case 'getNotifications':
        return handleGetNotifications(data, auth);
      case 'createNotification':
        return handleCreateNotification(data, auth);
      case 'markNotificationRead':
        return handleMarkNotificationRead(data, auth);

      // 系統設定相關
      case 'getSystemSettings':
        return handleGetSystemSettings(auth);
      case 'updateSystemSettings':
        return handleUpdateSystemSettings(data, auth);

      // 資料同步
      case 'syncAllData':
        return handleSyncAllData(auth);
      case 'migrateFromLocalStorage':
        return handleMigrateFromLocalStorage(data, auth);

      default:
        return ContentService
          .createTextOutput(JSON.stringify(API_RESPONSE.error('Unknown action: ' + action, 'INVALID_ACTION')))
          .setMimeType(ContentService.MimeType.JSON);
    }

  } catch (error) {
    console.error('API Error:', error);
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error('Internal server error: ' + error.message, 'INTERNAL_ERROR')))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * GET 請求處理（用於簡單的資料查詢）
 */
function doGet(e) {
  try {
    const action = e && e.parameter ? e.parameter.action : null;

    switch (action) {
      case 'health':
        return ContentService
          .createTextOutput(JSON.stringify(API_RESPONSE.success({
            status: 'healthy',
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            sheetsId: SPREADSHEET_ID
          })))
          .setMimeType(ContentService.MimeType.JSON);

      case 'test':
        return handleTestConnection();

      case 'getPublicSettings':
        return handleGetPublicSettings();

      default:
        return ContentService
          .createTextOutput(JSON.stringify(API_RESPONSE.error('Invalid GET request. Available actions: health, test, getPublicSettings', 'INVALID_REQUEST')))
          .setMimeType(ContentService.MimeType.JSON);
    }
  } catch (error) {
    console.error('GET request error:', error);
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error('GET request failed: ' + error.message, 'GET_ERROR')))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 測試連接函數
 */
function handleTestConnection() {
  try {
    // 測試 Sheets 連接
    const spreadsheet = getSpreadsheet();
    const sheets = spreadsheet.getSheets();

    const sheetInfo = sheets.map(sheet => ({
      name: sheet.getName(),
      rows: sheet.getLastRow(),
      cols: sheet.getLastColumn()
    }));

    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success({
        message: 'Connection test successful',
        sheetsId: spreadsheet.getId(),
        sheetsName: spreadsheet.getName(),
        sheetsCount: sheets.length,
        sheets: sheetInfo
      })))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error('Connection test failed: ' + error.message, 'CONNECTION_FAILED')))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// ==================== 工具函數 ====================

/**
 * 獲取 Google Sheets 實例
 */
function getSpreadsheet() {
  try {
    return SpreadsheetApp.openById(SPREADSHEET_ID);
  } catch (error) {
    throw new Error('無法開啟 Google Sheets，請檢查 SPREADSHEET_ID 設定');
  }
}

/**
 * 獲取指定工作表
 */
function getSheet(sheetName) {
  const spreadsheet = getSpreadsheet();
  let sheet = spreadsheet.getSheetByName(sheetName);
  
  if (!sheet) {
    // 如果工作表不存在，自動建立
    sheet = createSheet(spreadsheet, sheetName);
  }
  
  return sheet;
}

/**
 * 建立新工作表並設定標題行
 */
function createSheet(spreadsheet, sheetName) {
  const sheet = spreadsheet.insertSheet(sheetName);
  
  // 根據工作表類型設定標題行
  const headers = getSheetHeaders(sheetName);
  if (headers.length > 0) {
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    sheet.getRange(1, 1, 1, headers.length).setFontWeight('bold');
    sheet.setFrozenRows(1);
  }
  
  return sheet;
}

/**
 * 獲取工作表標題行
 */
function getSheetHeaders(sheetName) {
  const headerMap = {
    [SHEET_NAMES.USERS]: [
      'id', 'name', 'email', 'password', 'role', 'location', 'department', 'lineId',
      'approved', 'monthlyLeaveDays', 'overtimeBalance', 'annualSickLeaveQuota',
      'sickLeaveBalance', 'usedSickLeaveDays', 'weeklySchedule', 'registrationDate',
      'lastLoginDate', 'isActive'
    ],
    [SHEET_NAMES.SCHEDULE_REQUESTS]: [
      'id', 'employeeId', 'employeeName', 'month', 'status', 'scheduleData',
      'hasCompensatoryLeave', 'compensatoryDaysCount', 'hasSickLeave', 'sickLeaveDaysCount',
      'submittedAt', 'approver', 'approvedAt', 'comments', 'originalCompensatoryDays',
      'originalSickLeaveDays'
    ],
    [SHEET_NAMES.OVERTIME_RECORDS]: [
      'id', 'employeeId', 'employeeName', 'date', 'duration', 'type',
      'addedBy', 'addedDate', 'reason', 'isApproved'
    ],
    [SHEET_NAMES.NOTIFICATIONS]: [
      'id', 'type', 'title', 'message', 'targetUserId', 'isRead',
      'createdAt', 'createdBy', 'priority', 'category'
    ],
    [SHEET_NAMES.EMPLOYEE_SCHEDULES]: [
      'id', 'employeeId', 'employeeName', 'month', 'scheduleData',
      'totalWorkDays', 'totalLeaveDays', 'createdAt', 'updatedAt'
    ],
    [SHEET_NAMES.SHIFT_CHANGES]: [
      'id', 'employeeId', 'employeeName', 'originalDate', 'newDate',
      'originalShift', 'newShift', 'reason', 'status', 'requestedAt',
      'approver', 'approvedAt'
    ],
    [SHEET_NAMES.SYSTEM_SETTINGS]: [
      'settingKey', 'settingValue', 'category', 'description', 'updatedAt', 'updatedBy'
    ],
    [SHEET_NAMES.ACTIVITY_LOGS]: [
      'id', 'userId', 'userName', 'action', 'targetType', 'targetId',
      'details', 'ipAddress', 'userAgent', 'timestamp'
    ]
  };
  
  return headerMap[sheetName] || [];
}

/**
 * 驗證用戶身份
 */
function validateAuth(auth) {
  if (!auth || !auth.userId || !auth.token) {
    throw new Error('未提供有效的身份驗證資訊');
  }
  
  // 這裡可以實作更複雜的 token 驗證邏輯
  // 目前簡化為檢查用戶是否存在
  const user = getUserById(auth.userId);
  if (!user) {
    throw new Error('無效的用戶身份');
  }
  
  return user;
}

/**
 * 檢查用戶權限
 */
function checkPermission(user, action, targetData = null) {
  const { role } = user;
  
  // 管理員有所有權限
  if (role === 'admin') {
    return true;
  }
  
  // 主管權限檢查
  if (role === 'supervisor') {
    const supervisorActions = [
      'getScheduleRequests', 'approveScheduleRequest', 'getUsers',
      'getOvertimeRecords', 'createOvertimeRecord', 'getNotifications'
    ];
    return supervisorActions.includes(action);
  }
  
  // 員工權限檢查
  if (role === 'employee') {
    const employeeActions = [
      'createScheduleRequest', 'getScheduleRequests', 'getNotifications',
      'getOvertimeRecords'
    ];
    
    // 員工只能操作自己的資料
    if (targetData && targetData.employeeId && targetData.employeeId !== user.id) {
      return false;
    }
    
    return employeeActions.includes(action);
  }
  
  return false;
}

/**
 * 記錄活動日誌
 */
function logActivity(action, userId, userName, details) {
  try {
    const sheet = getSheet(SHEET_NAMES.ACTIVITY_LOGS);
    const id = 'log_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    
    const logData = [
      id,
      userId || 'system',
      userName || 'System',
      action,
      details.targetType || '',
      details.targetId || '',
      JSON.stringify(details),
      details.ipAddress || '',
      details.userAgent || '',
      new Date().toISOString()
    ];
    
    sheet.appendRow(logData);
  } catch (error) {
    console.error('記錄活動日誌失敗:', error);
  }
}

/**
 * 生成唯一 ID
 */
function generateId(prefix = 'id') {
  return prefix + '_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

/**
 * 將工作表資料轉換為物件陣列
 */
function sheetToObjects(sheet) {
  const data = sheet.getDataRange().getValues();
  if (data.length <= 1) return [];
  
  const headers = data[0];
  const objects = [];
  
  for (let i = 1; i < data.length; i++) {
    const obj = {};
    for (let j = 0; j < headers.length; j++) {
      const value = data[i][j];
      
      // 嘗試解析 JSON 字符串
      if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
        try {
          obj[headers[j]] = JSON.parse(value);
        } catch (e) {
          obj[headers[j]] = value;
        }
      } else {
        obj[headers[j]] = value;
      }
    }
    objects.push(obj);
  }
  
  return objects;
}

/**
 * 將物件陣列寫入工作表
 */
function objectsToSheet(sheet, objects, headers) {
  if (objects.length === 0) return;
  
  // 清除現有資料（保留標題行）
  const lastRow = sheet.getLastRow();
  if (lastRow > 1) {
    sheet.deleteRows(2, lastRow - 1);
  }
  
  // 準備資料
  const data = objects.map(obj => {
    return headers.map(header => {
      const value = obj[header];
      
      // 將物件和陣列轉換為 JSON 字符串
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value);
      }
      
      return value || '';
    });
  });
  
  // 寫入資料
  if (data.length > 0) {
    sheet.getRange(2, 1, data.length, headers.length).setValues(data);
  }
}

// ==================== 用戶管理 API ====================

/**
 * 獲取用戶列表
 */
function handleGetUsers(auth) {
  try {
    const user = validateAuth(auth);
    
    if (!checkPermission(user, 'getUsers')) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('權限不足', 'PERMISSION_DENIED')))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    const sheet = getSheet(SHEET_NAMES.USERS);
    const users = sheetToObjects(sheet);
    
    // 移除密碼欄位
    const safeUsers = users.map(u => {
      const { password, ...safeUser } = u;
      return safeUser;
    });
    
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success(safeUsers)))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 根據 ID 獲取用戶
 */
function getUserById(userId) {
  const sheet = getSheet(SHEET_NAMES.USERS);
  const users = sheetToObjects(sheet);
  return users.find(user => user.id === userId);
}

/**
 * 用戶身份驗證
 */
function handleAuthenticateUser(data) {
  try {
    const { email, password } = data;
    
    if (!email || !password) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('請提供電子郵件和密碼', 'MISSING_CREDENTIALS')))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    const sheet = getSheet(SHEET_NAMES.USERS);
    const users = sheetToObjects(sheet);
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('電子郵件或密碼錯誤', 'INVALID_CREDENTIALS')))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    if (!user.approved) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('帳戶尚未通過審核，請聯繫管理員', 'ACCOUNT_NOT_APPROVED')))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // 更新最後登入時間
    user.lastLoginDate = new Date().toISOString();
    updateUserInSheet(user);
    
    // 生成簡單的 token（實際應用中應使用更安全的方法）
    const token = 'token_' + user.id + '_' + Date.now();
    
    // 移除密碼
    const { password: _, ...safeUser } = user;
    
    logActivity('USER_LOGIN', user.id, user.name, {
      targetType: 'user',
      targetId: user.id
    });
    
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success({
        user: safeUser,
        token: token
      })))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 更新工作表中的用戶資料
 */
function updateUserInSheet(userData) {
  const sheet = getSheet(SHEET_NAMES.USERS);
  const users = sheetToObjects(sheet);
  const userIndex = users.findIndex(u => u.id === userData.id);
  
  if (userIndex !== -1) {
    users[userIndex] = userData;
    const headers = getSheetHeaders(SHEET_NAMES.USERS);
    objectsToSheet(sheet, users, headers);
  }
}

/**
 * 建立新用戶
 */
function handleCreateUser(data, auth) {
  try {
    const user = validateAuth(auth);

    if (!checkPermission(user, 'createUser')) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('權限不足', 'PERMISSION_DENIED')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const { name, email, password, role, location, department, lineId } = data;

    // 驗證必填欄位
    if (!name || !email || !password || !role) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('請填寫所有必填欄位', 'MISSING_FIELDS')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 檢查電子郵件是否已存在
    const sheet = getSheet(SHEET_NAMES.USERS);
    const users = sheetToObjects(sheet);
    const existingUser = users.find(u => u.email === email);

    if (existingUser) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('此電子郵件已被註冊', 'EMAIL_EXISTS')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 建立新用戶
    const newUser = {
      id: generateId('user'),
      name: name,
      email: email,
      password: password, // 實際應用中應該加密
      role: role,
      location: location || 'ciguang',
      department: department || '',
      lineId: lineId || '',
      approved: true, // 管理員建立的用戶自動審核通過
      monthlyLeaveDays: role === 'employee' ? 8 : 6,
      overtimeBalance: 0,
      annualSickLeaveQuota: role === 'employee' ? 30 : 20,
      sickLeaveBalance: role === 'employee' ? 30 : 20,
      usedSickLeaveDays: 0,
      weeklySchedule: {},
      registrationDate: new Date().toISOString(),
      lastLoginDate: '',
      isActive: true
    };

    // 添加到工作表
    users.push(newUser);
    const headers = getSheetHeaders(SHEET_NAMES.USERS);
    objectsToSheet(sheet, users, headers);

    logActivity('CREATE_USER', user.id, user.name, {
      targetType: 'user',
      targetId: newUser.id,
      newUserEmail: email,
      newUserRole: role
    });

    // 移除密碼
    const { password: _, ...safeUser } = newUser;

    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success(safeUser, '用戶建立成功')))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// ==================== 排班申請管理 API ====================

/**
 * 獲取排班申請列表
 */
function handleGetScheduleRequests(data, auth) {
  try {
    const user = validateAuth(auth);

    if (!checkPermission(user, 'getScheduleRequests')) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('權限不足', 'PERMISSION_DENIED')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const sheet = getSheet(SHEET_NAMES.SCHEDULE_REQUESTS);
    let requests = sheetToObjects(sheet);

    // 根據用戶角色過濾資料
    if (user.role === 'employee') {
      requests = requests.filter(r => r.employeeId === user.id);
    } else if (user.role === 'supervisor') {
      // 主管可以看到所有申請或部門內申請（根據需求調整）
      // 這裡暫時顯示所有申請
    }

    // 如果有特定查詢條件
    if (data && data.filters) {
      const { employeeId, month, status } = data.filters;

      if (employeeId) {
        requests = requests.filter(r => r.employeeId === employeeId);
      }

      if (month) {
        requests = requests.filter(r => r.month === month);
      }

      if (status) {
        requests = requests.filter(r => r.status === status);
      }
    }

    // 按提交時間排序（最新的在前）
    requests.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));

    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success(requests)))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 建立排班申請
 */
function handleCreateScheduleRequest(data, auth) {
  try {
    const user = validateAuth(auth);

    if (!checkPermission(user, 'createScheduleRequest', data)) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('權限不足', 'PERMISSION_DENIED')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const {
      month,
      scheduleData,
      hasCompensatoryLeave,
      compensatoryDaysCount,
      hasSickLeave,
      sickLeaveDaysCount
    } = data;

    // 驗證必填欄位
    if (!month || !scheduleData) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('請填寫所有必填欄位', 'MISSING_FIELDS')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 檢查是否已有同月份申請
    const sheet = getSheet(SHEET_NAMES.SCHEDULE_REQUESTS);
    const requests = sheetToObjects(sheet);
    const existingRequest = requests.find(r =>
      r.employeeId === user.id &&
      r.month === month &&
      r.status !== 'rejected'
    );

    if (existingRequest) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('該月份已有申請記錄', 'REQUEST_EXISTS')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 建立新申請
    const newRequest = {
      id: generateId('req'),
      employeeId: user.id,
      employeeName: user.name,
      month: month,
      status: 'pending',
      scheduleData: scheduleData,
      hasCompensatoryLeave: hasCompensatoryLeave || false,
      compensatoryDaysCount: compensatoryDaysCount || 0,
      hasSickLeave: hasSickLeave || false,
      sickLeaveDaysCount: sickLeaveDaysCount || 0,
      submittedAt: new Date().toISOString(),
      approver: '',
      approvedAt: '',
      comments: '',
      originalCompensatoryDays: 0,
      originalSickLeaveDays: 0
    };

    // 添加到工作表
    requests.push(newRequest);
    const headers = getSheetHeaders(SHEET_NAMES.SCHEDULE_REQUESTS);
    objectsToSheet(sheet, requests, headers);

    logActivity('CREATE_SCHEDULE_REQUEST', user.id, user.name, {
      targetType: 'schedule_request',
      targetId: newRequest.id,
      month: month,
      compensatoryDays: compensatoryDaysCount,
      sickLeaveDays: sickLeaveDaysCount
    });

    // 發送通知給主管和管理員
    createNotificationForSupervisors('schedule_submission', '新排班申請',
      `${user.name} 提交了 ${month} 的排班申請，請審核。`, {
        requestId: newRequest.id,
        employeeId: user.id,
        month: month
      });

    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success(newRequest, '排班申請提交成功')))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * 審核排班申請
 */
function handleApproveScheduleRequest(data, auth) {
  try {
    const user = validateAuth(auth);

    if (!checkPermission(user, 'approveScheduleRequest')) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('權限不足', 'PERMISSION_DENIED')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const { requestId, status, comments } = data;

    if (!requestId || !status) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('請提供申請ID和審核狀態', 'MISSING_FIELDS')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    // 獲取申請記錄
    const sheet = getSheet(SHEET_NAMES.SCHEDULE_REQUESTS);
    const requests = sheetToObjects(sheet);
    const requestIndex = requests.findIndex(r => r.id === requestId);

    if (requestIndex === -1) {
      return ContentService
        .createTextOutput(JSON.stringify(API_RESPONSE.error('找不到申請記錄', 'REQUEST_NOT_FOUND')))
        .setMimeType(ContentService.MimeType.JSON);
    }

    const request = requests[requestIndex];

    // 更新申請狀態
    request.status = status;
    request.approver = user.name;
    request.approvedAt = new Date().toISOString();
    request.comments = comments || '';

    // 如果批准申請，需要處理相關業務邏輯
    if (status === 'approved') {
      // 處理補休扣減
      if (request.hasCompensatoryLeave && request.compensatoryDaysCount > 0) {
        updateEmployeeOvertimeBalance(request.employeeId, -request.compensatoryDaysCount);
      }

      // 處理事病假扣減
      if (request.hasSickLeave && request.sickLeaveDaysCount > 0) {
        updateEmployeeSickLeaveBalance(request.employeeId, request.sickLeaveDaysCount);
      }

      // 建立員工排班記錄
      createEmployeeSchedule(request);
    }

    // 更新工作表
    requests[requestIndex] = request;
    const headers = getSheetHeaders(SHEET_NAMES.SCHEDULE_REQUESTS);
    objectsToSheet(sheet, requests, headers);

    logActivity('APPROVE_SCHEDULE_REQUEST', user.id, user.name, {
      targetType: 'schedule_request',
      targetId: requestId,
      status: status,
      employeeId: request.employeeId
    });

    // 發送通知給申請人
    createNotificationForUser(request.employeeId, 'approval_result', '排班審核結果',
      `您的 ${request.month} 排班申請已${status === 'approved' ? '批准' : '拒絕'}。${comments ? '備註：' + comments : ''}`, {
        requestId: requestId,
        status: status
      });

    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.success(request, '審核完成')))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify(API_RESPONSE.error(error.message)))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// ==================== 輔助函數 ====================

/**
 * 更新員工額外班餘額
 */
function updateEmployeeOvertimeBalance(employeeId, change) {
  const userSheet = getSheet(SHEET_NAMES.USERS);
  const users = sheetToObjects(userSheet);
  const userIndex = users.findIndex(u => u.id === employeeId);

  if (userIndex !== -1) {
    users[userIndex].overtimeBalance = (users[userIndex].overtimeBalance || 0) + change;
    const headers = getSheetHeaders(SHEET_NAMES.USERS);
    objectsToSheet(userSheet, users, headers);
  }
}

/**
 * 更新員工事病假餘額
 */
function updateEmployeeSickLeaveBalance(employeeId, usedDays) {
  const userSheet = getSheet(SHEET_NAMES.USERS);
  const users = sheetToObjects(userSheet);
  const userIndex = users.findIndex(u => u.id === employeeId);

  if (userIndex !== -1) {
    users[userIndex].usedSickLeaveDays = (users[userIndex].usedSickLeaveDays || 0) + usedDays;
    users[userIndex].sickLeaveBalance = users[userIndex].annualSickLeaveQuota - users[userIndex].usedSickLeaveDays;
    const headers = getSheetHeaders(SHEET_NAMES.USERS);
    objectsToSheet(userSheet, users, headers);
  }
}

/**
 * 建立員工排班記錄
 */
function createEmployeeSchedule(request) {
  const sheet = getSheet(SHEET_NAMES.EMPLOYEE_SCHEDULES);
  const schedules = sheetToObjects(sheet);

  // 檢查是否已有該月份排班記錄
  const existingIndex = schedules.findIndex(s =>
    s.employeeId === request.employeeId && s.month === request.month
  );

  const scheduleRecord = {
    id: existingIndex !== -1 ? schedules[existingIndex].id : generateId('sch'),
    employeeId: request.employeeId,
    employeeName: request.employeeName,
    month: request.month,
    scheduleData: request.scheduleData,
    totalWorkDays: calculateWorkDays(request.scheduleData),
    totalLeaveDays: calculateLeaveDays(request.scheduleData),
    createdAt: existingIndex !== -1 ? schedules[existingIndex].createdAt : new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  if (existingIndex !== -1) {
    schedules[existingIndex] = scheduleRecord;
  } else {
    schedules.push(scheduleRecord);
  }

  const headers = getSheetHeaders(SHEET_NAMES.EMPLOYEE_SCHEDULES);
  objectsToSheet(sheet, schedules, headers);
}

/**
 * 計算工作天數
 */
function calculateWorkDays(scheduleData) {
  let workDays = 0;
  for (const day in scheduleData) {
    if (scheduleData[day] && scheduleData[day].shift && scheduleData[day].shift !== 'leave') {
      workDays++;
    }
  }
  return workDays;
}

/**
 * 計算休假天數
 */
function calculateLeaveDays(scheduleData) {
  let leaveDays = 0;
  for (const day in scheduleData) {
    if (scheduleData[day] && scheduleData[day].shift === 'leave') {
      leaveDays++;
    }
  }
  return leaveDays;
}

/**
 * 為主管和管理員建立通知
 */
function createNotificationForSupervisors(type, title, message, metadata) {
  const userSheet = getSheet(SHEET_NAMES.USERS);
  const users = sheetToObjects(userSheet);
  const supervisors = users.filter(u => u.role === 'supervisor' || u.role === 'admin');

  supervisors.forEach(supervisor => {
    createNotificationForUser(supervisor.id, type, title, message, metadata);
  });
}

/**
 * 為特定用戶建立通知
 */
function createNotificationForUser(userId, type, title, message, metadata) {
  const sheet = getSheet(SHEET_NAMES.NOTIFICATIONS);
  const notifications = sheetToObjects(sheet);

  const notification = {
    id: generateId('notif'),
    type: type,
    title: title,
    message: message,
    targetUserId: userId,
    isRead: false,
    createdAt: new Date().toISOString(),
    createdBy: 'system',
    priority: 'medium',
    category: 'schedule'
  };

  notifications.push(notification);
  const headers = getSheetHeaders(SHEET_NAMES.NOTIFICATIONS);
  objectsToSheet(sheet, notifications, headers);
}
