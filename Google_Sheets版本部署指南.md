# Google Sheets 版本部署指南

## 🎯 部署概覽

本指南將協助您將工作排班管理系統從 localStorage 版本升級為 Google Sheets 雲端協作版本。

### **升級優勢**
- ☁️ **雲端儲存**：資料安全備份，永不遺失
- 👥 **多人協作**：支援多用戶同時操作
- 🔄 **即時同步**：資料變更即時更新
- 📱 **跨裝置**：任何裝置都能訪問
- 🔒 **權限控制**：基於角色的資料訪問

## 📋 部署前準備

### **1. 必要條件**
- ✅ Google 帳戶
- ✅ Google Sheets 訪問權限
- ✅ Google Apps Script 使用權限
- ✅ 現有的 v0.9.1 系統

### **2. 技能要求**
- 🔧 基本的 Google Sheets 操作
- 🔧 簡單的複製貼上操作
- 🔧 基本的網址設定

## 🚀 部署步驟

### **步驟 1：建立 Google Sheets 資料庫**

#### **1.1 建立新的 Google Sheets**
1. 前往 [Google Sheets](https://sheets.google.com)
2. 點擊「建立新試算表」
3. 將試算表重新命名為：`工作排班管理系統`

#### **1.2 建立工作表**
依序建立以下工作表（點擊左下角的「+」號）：

1. **Users**（用戶資料）
2. **ScheduleRequests**（排班申請）
3. **OvertimeRecords**（額外班記錄）
4. **Notifications**（通知記錄）
5. **EmployeeSchedules**（員工排班）
6. **ShiftChanges**（調班記錄）
7. **SystemSettings**（系統設定）
8. **ActivityLogs**（操作日誌）

#### **1.3 設定工作表標題行**

**Users 工作表標題行（第1行）：**
```
id | name | email | password | role | location | department | lineId | approved | monthlyLeaveDays | overtimeBalance | annualSickLeaveQuota | sickLeaveBalance | usedSickLeaveDays | weeklySchedule | registrationDate | lastLoginDate | isActive
```

**ScheduleRequests 工作表標題行（第1行）：**
```
id | employeeId | employeeName | month | status | scheduleData | hasCompensatoryLeave | compensatoryDaysCount | hasSickLeave | sickLeaveDaysCount | submittedAt | approver | approvedAt | comments | originalCompensatoryDays | originalSickLeaveDays
```

**OvertimeRecords 工作表標題行（第1行）：**
```
id | employeeId | employeeName | date | duration | type | addedBy | addedDate | reason | isApproved
```

**Notifications 工作表標題行（第1行）：**
```
id | type | title | message | targetUserId | isRead | createdAt | createdBy | priority | category
```

**EmployeeSchedules 工作表標題行（第1行）：**
```
id | employeeId | employeeName | month | scheduleData | totalWorkDays | totalLeaveDays | createdAt | updatedAt
```

**ShiftChanges 工作表標題行（第1行）：**
```
id | employeeId | employeeName | originalDate | newDate | originalShift | newShift | reason | status | requestedAt | approver | approvedAt
```

**SystemSettings 工作表標題行（第1行）：**
```
settingKey | settingValue | category | description | updatedAt | updatedBy
```

**ActivityLogs 工作表標題行（第1行）：**
```
id | userId | userName | action | targetType | targetId | details | ipAddress | userAgent | timestamp
```

#### **1.4 取得 Sheets ID**
1. 在瀏覽器網址列中找到 Sheets URL
2. 複製 `/d/` 和 `/edit` 之間的字串
3. 例如：`https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit`
4. Sheets ID 就是：`1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

### **步驟 2：建立 Google Apps Script 後端**

#### **2.1 開啟 Google Apps Script**
1. 前往 [Google Apps Script](https://script.google.com)
2. 點擊「新增專案」
3. 將專案重新命名為：`工作排班管理系統API`

#### **2.2 設定程式碼**
1. 刪除預設的 `myFunction`
2. 複製 `Google_Apps_Script_後端.js` 的完整內容
3. 貼上到 Apps Script 編輯器中
4. 修改第 10 行的 `SPREADSHEET_ID`：
   ```javascript
   const SPREADSHEET_ID = '您的_SHEETS_ID_在這裡';
   ```

#### **2.3 設定權限**
1. 點擊「儲存」（Ctrl+S）
2. 點擊「執行」按鈕測試
3. 授權 Apps Script 訪問 Google Sheets
4. 確認沒有錯誤訊息

#### **2.4 部署為 Web App**
1. 點擊右上角「部署」→「新增部署作業」
2. 選擇類型：「網頁應用程式」
3. 說明：`工作排班管理系統API v1.0`
4. 執行身分：`我`
5. 存取權：`任何人`
6. 點擊「部署」
7. **複製 Web App URL**（重要！）

### **步驟 3：修改前端程式碼**

#### **3.1 備份現有系統**
1. 複製 `schedule-app-simple.html` 為 `schedule-app-simple-backup.html`

#### **3.2 整合 Google Sheets API**
1. 在 `schedule-app-simple.html` 的 `<head>` 區段添加：
   ```html
   <!-- Google Sheets 整合 -->
   <script src="前端Google_Sheets整合.js"></script>
   <script src="資料遷移腳本.js"></script>
   ```

2. 修改 `前端Google_Sheets整合.js` 第 8 行：
   ```javascript
   const GOOGLE_APPS_SCRIPT_URL = '您的_WEB_APP_URL_在這裡';
   ```

#### **3.3 替換資料存取函數**
將原本的 localStorage 函數替換為 Google Sheets API 呼叫：

**原本：**
```javascript
function saveSystemData() {
    localStorage.setItem('workdaysSystemData', JSON.stringify(systemData));
}
```

**替換為：**
```javascript
async function saveSystemData() {
    try {
        await window.workScheduleAPI.syncAllData();
    } catch (error) {
        console.error('儲存資料失敗:', error);
    }
}
```

### **步驟 4：執行資料遷移**

#### **4.1 準備遷移**
1. 確保現有系統正常運作
2. 確保有網路連線
3. 確保 Google Apps Script 正常運作

#### **4.2 執行遷移**
1. 開啟修改後的系統
2. 按 F12 開啟開發者工具
3. 在控制台執行：
   ```javascript
   showMigrationConfirmDialog();
   ```
4. 確認遷移資訊後點擊「確定」
5. 等待遷移完成

#### **4.3 驗證遷移結果**
1. 檢查 Google Sheets 中是否有資料
2. 測試登入功能
3. 測試基本操作（新增、編輯、刪除）
4. 確認多人協作功能

### **步驟 5：部署到 Google 協作平台**

#### **5.1 建立 Google 協作平台**
1. 前往 [Google 協作平台](https://sites.google.com)
2. 點擊「建立新網站」
3. 選擇「空白」範本
4. 網站名稱：`工作排班管理系統`

#### **5.2 上傳系統檔案**
1. 點擊「插入」→「嵌入程式碼」
2. 選擇「上傳」
3. 上傳修改後的 `schedule-app-simple.html`
4. 設定為全螢幕顯示

#### **5.3 設定網站權限**
1. 點擊右上角「分享」
2. 設定檢視權限：
   - **內部使用**：限制為組織內成員
   - **公開使用**：任何人都可檢視
3. 複製網站連結

#### **5.4 發布網站**
1. 點擊右上角「發布」
2. 選擇網址：`您的組織名稱-工作排班系統`
3. 點擊「發布」
4. 記錄發布的網址

## 🔧 設定調整

### **權限設定**
```javascript
// 在 Google Apps Script 中調整權限
function checkPermission(user, action, targetData = null) {
    // 根據您的組織需求調整權限邏輯
}
```

### **通知設定**
```javascript
// 設定 LINE 通知（可選）
const LINE_BOT_CONFIG = {
    channelAccessToken: '您的_LINE_BOT_TOKEN',
    channelSecret: '您的_LINE_BOT_SECRET'
};
```

## 🧪 測試檢查清單

### **基本功能測試**
- [ ] 用戶登入/登出
- [ ] 用戶註冊（如果啟用）
- [ ] 排班申請提交
- [ ] 排班申請審核
- [ ] 額外班管理
- [ ] 通知系統
- [ ] 系統設定

### **多人協作測試**
- [ ] 多用戶同時登入
- [ ] 資料即時同步
- [ ] 權限控制
- [ ] 衝突處理

### **效能測試**
- [ ] 大量資料載入
- [ ] 網路中斷恢復
- [ ] 快取機制
- [ ] 回應時間

## 🚨 常見問題

### **Q1：Google Apps Script 權限錯誤**
**A1：** 確保已正確授權 Apps Script 訪問 Google Sheets，並檢查 Sheets ID 是否正確。

### **Q2：資料遷移失敗**
**A2：** 檢查網路連線、API URL 設定，並查看瀏覽器控制台的錯誤訊息。

### **Q3：多人同時編輯衝突**
**A3：** 系統會自動處理大部分衝突，如遇問題請重新載入頁面。

### **Q4：效能緩慢**
**A4：** 啟用快取機制，減少不必要的 API 呼叫，考慮分頁載入大量資料。

## 📞 技術支援

如果在部署過程中遇到問題：

1. **檢查控制台錯誤**：按 F12 查看詳細錯誤訊息
2. **驗證設定**：確認所有 URL 和 ID 設定正確
3. **測試連線**：確認 Google Apps Script 可正常訪問
4. **查看日誌**：檢查 Google Apps Script 的執行日誌

## 🎉 部署完成

恭喜！您已成功將工作排班管理系統升級為 Google Sheets 雲端協作版本。

### **現在您可以：**
- ☁️ 享受雲端資料安全
- 👥 支援多人同時使用
- 🔄 即時資料同步
- 📱 跨裝置訪問
- 🔒 完整的權限控制

### **下一步建議：**
1. 培訓用戶使用新系統
2. 建立資料備份計劃
3. 監控系統效能
4. 收集用戶回饋
5. 持續優化功能

**祝您使用愉快！** 🚀
